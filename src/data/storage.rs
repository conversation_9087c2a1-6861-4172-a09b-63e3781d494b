//! 数据存储模块
//!
//! 负责数据的持久化存储，使用 SQLite 作为本地数据库

use crate::{Result, QuantGridError, info, debug};
use serde::{Deserialize, Serialize};
use rusqlite::{Connection, params};

/// 数据库错误
#[derive(thiserror::Error, Debug)]
pub enum DatabaseError {
    #[error("连接错误: {0}")]
    Connection(String),
    
    #[error("查询错误: {0}")]
    Query(String),
    
    #[error("序列化错误: {0}")]
    Serialization(String),
}

/// 数据库管理器
pub struct DatabaseManager {
    /// 数据库连接
    connection: Option<Connection>,
    /// 数据库路径
    db_path: String,
    /// 是否已连接
    connected: bool,
}

impl std::fmt::Debug for DatabaseManager {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("DatabaseManager")
            .field("db_path", &self.db_path)
            .field("connected", &self.connected)
            .finish()
    }
}

impl DatabaseManager {
    /// 创建新的数据库管理器
    pub fn new(db_path: String) -> Self {
        info!("创建数据库管理器: {}", db_path);

        Self {
            connection: None,
            db_path,
            connected: false,
        }
    }
    
    /// 连接数据库
    pub async fn connect(&mut self) -> Result<()> {
        info!("连接数据库: {}", self.db_path);

        // 创建 DuckDB 连接
        let connection = Connection::open(&self.db_path)
            .map_err(|e| QuantGridError::Config(format!("数据库连接失败: {}", e)))?;

        self.connection = Some(connection);
        self.connected = true;

        // 初始化数据库表
        self.initialize_tables().await?;

        info!("数据库连接成功: {}", self.db_path);
        Ok(())
    }
    
    /// 断开数据库连接
    pub async fn disconnect(&mut self) -> Result<()> {
        info!("断开数据库连接");
        self.connection = None;
        self.connected = false;
        Ok(())
    }
    
    /// 初始化数据库表
    pub async fn initialize_tables(&self) -> Result<()> {
        let conn = self.connection.as_ref()
            .ok_or_else(|| QuantGridError::Config("数据库未连接".to_string()))?;

        info!("初始化数据库表");

        // 创建机器人配置表
        conn.execute(
            "CREATE TABLE IF NOT EXISTS bot_configs (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                symbol TEXT NOT NULL,
                config_json TEXT NOT NULL,
                created_at TIMESTAMP NOT NULL,
                updated_at TIMESTAMP NOT NULL,
                enabled BOOLEAN NOT NULL DEFAULT true
            )",
            [],
        ).map_err(|e| QuantGridError::Config(format!("创建bot_configs表失败: {}", e)))?;

        // 创建交易记录表
        conn.execute(
            "CREATE TABLE IF NOT EXISTS trade_records (
                id TEXT PRIMARY KEY,
                bot_id TEXT NOT NULL,
                symbol TEXT NOT NULL,
                side TEXT NOT NULL,
                quantity TEXT NOT NULL,
                price TEXT NOT NULL,
                total_amount TEXT NOT NULL,
                fee TEXT NOT NULL,
                tx_hash TEXT,
                status TEXT NOT NULL,
                created_at TIMESTAMP NOT NULL,
                executed_at TIMESTAMP,
                FOREIGN KEY (bot_id) REFERENCES bot_configs(id)
            )",
            [],
        ).map_err(|e| QuantGridError::Config(format!("创建trade_records表失败: {}", e)))?;

        // 创建价格历史表
        conn.execute(
            "CREATE TABLE IF NOT EXISTS price_history (
                id INTEGER PRIMARY KEY,
                symbol TEXT NOT NULL,
                price DECIMAL NOT NULL,
                volume DECIMAL,
                timestamp TIMESTAMP NOT NULL,
                source TEXT NOT NULL
            )",
            [],
        ).map_err(|e| QuantGridError::Config(format!("创建price_history表失败: {}", e)))?;

        // 创建订单历史表
        conn.execute(
            "CREATE TABLE IF NOT EXISTS order_history (
                id TEXT PRIMARY KEY,
                bot_id TEXT NOT NULL,
                symbol TEXT NOT NULL,
                order_type TEXT NOT NULL,
                quantity DECIMAL NOT NULL,
                price DECIMAL NOT NULL,
                status TEXT NOT NULL,
                grid_level INTEGER,
                created_at TIMESTAMP NOT NULL,
                updated_at TIMESTAMP NOT NULL,
                FOREIGN KEY (bot_id) REFERENCES bot_configs(id)
            )",
            [],
        ).map_err(|e| QuantGridError::Config(format!("创建order_history表失败: {}", e)))?;

        info!("数据库表初始化完成");
        Ok(())
    }
    
    /// 保存机器人配置
    pub async fn save_bot_config(&self, config: &BotConfig) -> Result<()> {
        let conn = self.connection.as_ref()
            .ok_or_else(|| QuantGridError::Config("数据库未连接".to_string()))?;

        debug!("保存机器人配置: {}", config.id);

        // 使用 UPSERT 语法 (INSERT OR REPLACE)
        conn.execute(
            "INSERT OR REPLACE INTO bot_configs
             (id, name, symbol, config_json, created_at, updated_at, enabled)
             VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7)",
            params![
                config.id,
                config.name,
                config.symbol,
                config.config_json,
                config.created_at.to_rfc3339(),
                config.updated_at.to_rfc3339(),
                config.enabled
            ],
        ).map_err(|e| QuantGridError::Config(format!("保存机器人配置失败: {}", e)))?;

        debug!("机器人配置保存成功: {}", config.id);
        Ok(())
    }
    
    /// 加载机器人配置
    pub async fn load_bot_config(&self, bot_id: &str) -> Result<Option<BotConfig>> {
        let conn = self.connection.as_ref()
            .ok_or_else(|| QuantGridError::Config("数据库未连接".to_string()))?;

        debug!("加载机器人配置: {}", bot_id);

        let mut stmt = conn.prepare(
            "SELECT id, name, symbol, config_json, created_at, updated_at, enabled
             FROM bot_configs WHERE id = ?1"
        ).map_err(|e| QuantGridError::Config(format!("准备查询语句失败: {}", e)))?;

        let mut rows = stmt.query(params![bot_id])
            .map_err(|e| QuantGridError::Config(format!("查询机器人配置失败: {}", e)))?;

        if let Some(row) = rows.next()
            .map_err(|e| QuantGridError::Config(format!("读取查询结果失败: {}", e)))? {

            let created_at_str: String = row.get(4)?;
            let updated_at_str: String = row.get(5)?;

            let config = BotConfig {
                id: row.get(0)?,
                name: row.get(1)?,
                symbol: row.get(2)?,
                config_json: row.get(3)?,
                created_at: chrono::DateTime::parse_from_rfc3339(&created_at_str)
                    .map_err(|e| QuantGridError::Config(format!("解析创建时间失败: {}", e)))?
                    .with_timezone(&chrono::Utc),
                updated_at: chrono::DateTime::parse_from_rfc3339(&updated_at_str)
                    .map_err(|e| QuantGridError::Config(format!("解析更新时间失败: {}", e)))?
                    .with_timezone(&chrono::Utc),
                enabled: row.get(6)?,
            };

            debug!("机器人配置加载成功: {}", bot_id);
            Ok(Some(config))
        } else {
            debug!("机器人配置不存在: {}", bot_id);
            Ok(None)
        }
    }
    
    /// 保存交易记录
    pub async fn save_trade_record(&self, record: &TradeRecord) -> Result<()> {
        let conn = self.connection.as_ref()
            .ok_or_else(|| QuantGridError::Config("数据库未连接".to_string()))?;

        debug!("保存交易记录: {}", record.id);

        conn.execute(
            "INSERT OR REPLACE INTO trade_records
             (id, bot_id, symbol, side, quantity, price, total_amount, fee, tx_hash, status, created_at, executed_at)
             VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11, ?12)",
            params![
                record.id,
                record.bot_id,
                record.symbol,
                record.side,
                record.quantity.to_string(),
                record.price.to_string(),
                record.total_amount.to_string(),
                record.fee.to_string(),
                record.tx_hash,
                record.status,
                record.created_at.to_rfc3339(),
                record.executed_at.map(|t| t.to_rfc3339())
            ],
        ).map_err(|e| QuantGridError::Config(format!("保存交易记录失败: {}", e)))?;

        debug!("交易记录保存成功: {}", record.id);
        Ok(())
    }
    
    /// 查询交易记录
    pub async fn get_trade_records(
        &self,
        bot_id: &str,
        limit: Option<u32>,
    ) -> Result<Vec<TradeRecord>> {
        let conn = self.connection.as_ref()
            .ok_or_else(|| QuantGridError::Config("数据库未连接".to_string()))?;

        debug!("查询交易记录: bot_id={} limit={:?}", bot_id, limit);

        let limit_clause = if let Some(limit) = limit {
            format!(" LIMIT {}", limit)
        } else {
            String::new()
        };

        let sql = format!(
            "SELECT id, bot_id, symbol, side, quantity, price, total_amount, fee, tx_hash, status, created_at, executed_at
             FROM trade_records WHERE bot_id = ?1 ORDER BY created_at DESC{}",
            limit_clause
        );

        let mut stmt = conn.prepare(&sql)
            .map_err(|e| QuantGridError::Config(format!("准备查询语句失败: {}", e)))?;

        let rows = stmt.query_map(params![bot_id], |row| {
            let created_at_str: String = row.get(10)?;
            let executed_at_str: Option<String> = row.get(11)?;

            let quantity_str: String = row.get(4)?;
            let price_str: String = row.get(5)?;
            let total_amount_str: String = row.get(6)?;
            let fee_str: String = row.get(7)?;

            Ok(TradeRecord {
                id: row.get(0)?,
                bot_id: row.get(1)?,
                symbol: row.get(2)?,
                side: row.get(3)?,
                quantity: quantity_str.parse().map_err(|_| rusqlite::Error::InvalidColumnType(
                    4, "quantity".to_string(), rusqlite::types::Type::Text
                ))?,
                price: price_str.parse().map_err(|_| rusqlite::Error::InvalidColumnType(
                    5, "price".to_string(), rusqlite::types::Type::Text
                ))?,
                total_amount: total_amount_str.parse().map_err(|_| rusqlite::Error::InvalidColumnType(
                    6, "total_amount".to_string(), rusqlite::types::Type::Text
                ))?,
                fee: fee_str.parse().map_err(|_| rusqlite::Error::InvalidColumnType(
                    7, "fee".to_string(), rusqlite::types::Type::Text
                ))?,
                tx_hash: row.get(8)?,
                status: row.get(9)?,
                created_at: chrono::DateTime::parse_from_rfc3339(&created_at_str)
                    .map_err(|_| rusqlite::Error::InvalidColumnType(
                        10, "created_at".to_string(), rusqlite::types::Type::Text
                    ))?
                    .with_timezone(&chrono::Utc),
                executed_at: executed_at_str.map(|s| {
                    chrono::DateTime::parse_from_rfc3339(&s)
                        .map(|dt| dt.with_timezone(&chrono::Utc))
                }).transpose().map_err(|_| rusqlite::Error::InvalidColumnType(
                    11, "executed_at".to_string(), rusqlite::types::Type::Text
                ))?,
            })
        }).map_err(|e| QuantGridError::Config(format!("查询交易记录失败: {}", e)))?;

        let mut records = Vec::new();
        for row_result in rows {
            let record = row_result
                .map_err(|e| QuantGridError::Config(format!("解析交易记录失败: {}", e)))?;
            records.push(record);
        }

        debug!("查询到 {} 条交易记录", records.len());
        Ok(records)
    }
    
    /// 获取所有机器人配置
    pub async fn get_all_bot_configs(&self) -> Result<Vec<BotConfig>> {
        let conn = self.connection.as_ref()
            .ok_or_else(|| QuantGridError::Config("数据库未连接".to_string()))?;

        debug!("查询所有机器人配置");

        let mut stmt = conn.prepare(
            "SELECT id, name, symbol, config_json, created_at, updated_at, enabled
             FROM bot_configs ORDER BY created_at DESC"
        ).map_err(|e| QuantGridError::Config(format!("准备查询语句失败: {}", e)))?;

        let rows = stmt.query_map([], |row| {
            let created_at_str: String = row.get(4)?;
            let updated_at_str: String = row.get(5)?;

            Ok(BotConfig {
                id: row.get(0)?,
                name: row.get(1)?,
                symbol: row.get(2)?,
                config_json: row.get(3)?,
                created_at: chrono::DateTime::parse_from_rfc3339(&created_at_str)
                    .map_err(|_| rusqlite::Error::InvalidColumnType(
                        4, "created_at".to_string(), rusqlite::types::Type::Text
                    ))?
                    .with_timezone(&chrono::Utc),
                updated_at: chrono::DateTime::parse_from_rfc3339(&updated_at_str)
                    .map_err(|_| rusqlite::Error::InvalidColumnType(
                        5, "updated_at".to_string(), rusqlite::types::Type::Text
                    ))?
                    .with_timezone(&chrono::Utc),
                enabled: row.get(6)?,
            })
        }).map_err(|e| QuantGridError::Config(format!("查询机器人配置失败: {}", e)))?;

        let mut configs = Vec::new();
        for row_result in rows {
            let config = row_result
                .map_err(|e| QuantGridError::Config(format!("解析机器人配置失败: {}", e)))?;
            configs.push(config);
        }

        debug!("查询到 {} 个机器人配置", configs.len());
        Ok(configs)
    }

    /// 保存价格历史数据
    pub async fn save_price_data(&self, symbol: &str, price: rust_decimal::Decimal, volume: Option<rust_decimal::Decimal>, source: &str) -> Result<()> {
        let conn = self.connection.as_ref()
            .ok_or_else(|| QuantGridError::Config("数据库未连接".to_string()))?;

        conn.execute(
            "INSERT INTO price_history (symbol, price, volume, timestamp, source)
             VALUES (?1, ?2, ?3, ?4, ?5)",
            params![
                symbol,
                price.to_string(),
                volume.map(|v| v.to_string()),
                chrono::Utc::now().to_rfc3339(),
                source
            ],
        ).map_err(|e| QuantGridError::Config(format!("保存价格数据失败: {}", e)))?;

        Ok(())
    }

    /// 删除机器人配置
    pub async fn delete_bot_config(&self, bot_id: &str) -> Result<()> {
        let conn = self.connection.as_ref()
            .ok_or_else(|| QuantGridError::Config("数据库未连接".to_string()))?;

        debug!("删除机器人配置: {}", bot_id);

        // 删除相关的交易记录
        conn.execute(
            "DELETE FROM trade_records WHERE bot_id = ?1",
            params![bot_id],
        ).map_err(|e| QuantGridError::Config(format!("删除交易记录失败: {}", e)))?;

        // 删除机器人配置
        conn.execute(
            "DELETE FROM bot_configs WHERE id = ?1",
            params![bot_id],
        ).map_err(|e| QuantGridError::Config(format!("删除机器人配置失败: {}", e)))?;

        debug!("机器人配置删除成功: {}", bot_id);
        Ok(())
    }

    /// 是否已连接
    pub fn is_connected(&self) -> bool {
        self.connected
    }
}

/// 机器人配置 (数据库存储用)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BotConfig {
    pub id: String,
    pub name: String,
    pub symbol: String,
    pub config_json: String, // JSON序列化的配置
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub enabled: bool,
}

/// 交易记录 (数据库存储用)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradeRecord {
    pub id: String,
    pub bot_id: String,
    pub symbol: String,
    pub side: String, // "buy" 或 "sell"
    pub quantity: rust_decimal::Decimal,
    pub price: rust_decimal::Decimal,
    pub total_amount: rust_decimal::Decimal,
    pub fee: rust_decimal::Decimal,
    pub tx_hash: Option<String>,
    pub status: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub executed_at: Option<chrono::DateTime<chrono::Utc>>,
}
