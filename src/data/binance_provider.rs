//! Binance API 市场数据提供者
//! 
//! 从 Binance API 获取真实的市场数据

use crate::{Result, QuantGridError, info, debug};
use super::market_data::{MarketDataProvider, PriceData, KlineData};
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use std::str::FromStr;

/// Binance API 响应 - 价格数据
#[derive(Debug, Deserialize)]
struct BinancePriceResponse {
    symbol: String,
    price: String,
}

/// Binance API 响应 - K线数据
#[derive(Debug, Deserialize)]
struct BinanceKlineResponse {
    #[serde(rename = "0")]
    open_time: i64,
    #[serde(rename = "1")]
    open: String,
    #[serde(rename = "2")]
    high: String,
    #[serde(rename = "3")]
    low: String,
    #[serde(rename = "4")]
    close: String,
    #[serde(rename = "5")]
    volume: String,
}

/// Binance 市场数据提供者
#[derive(Debug)]
pub struct BinanceMarketDataProvider {
    /// HTTP 客户端
    client: reqwest::Client,
    /// API 基础 URL
    base_url: String,
}

impl BinanceMarketDataProvider {
    /// 创建新的 Binance 数据提供者
    pub fn new() -> Self {
        Self {
            client: reqwest::Client::new(),
            base_url: "https://api.binance.com".to_string(),
        }
    }
    
    /// 转换交易对符号格式 (BNB/USDT -> BNBUSDT)
    fn convert_symbol(&self, symbol: &str) -> String {
        symbol.replace('/', "")
    }
    
    /// 解析价格字符串为 Decimal
    fn parse_price(&self, price_str: &str) -> Result<Decimal> {
        Decimal::from_str(price_str)
            .map_err(|e| QuantGridError::Data(format!("价格解析失败: {}", e)))
    }
}

impl Default for BinanceMarketDataProvider {
    fn default() -> Self {
        Self::new()
    }
}

impl MarketDataProvider for BinanceMarketDataProvider {
    async fn get_price(&self, symbol: &str) -> Result<PriceData> {
        let binance_symbol = self.convert_symbol(symbol);
        let url = format!("{}/api/v3/ticker/price?symbol={}", self.base_url, binance_symbol);
        
        debug!("获取价格数据: {} -> {}", symbol, binance_symbol);
        
        let response = self.client
            .get(&url)
            .send()
            .await
            .map_err(|e| QuantGridError::Data(format!("API 请求失败: {}", e)))?;
        
        if !response.status().is_success() {
            return Err(QuantGridError::Data(
                format!("API 返回错误状态: {}", response.status())
            ));
        }
        
        let price_response: BinancePriceResponse = response
            .json()
            .await
            .map_err(|e| QuantGridError::Data(format!("JSON 解析失败: {}", e)))?;
        
        let price = self.parse_price(&price_response.price)?;
        let timestamp = chrono::Utc::now();
        
        let price_data = PriceData {
            symbol: symbol.to_string(),
            price,
            timestamp,
            volume: None, // Binance 价格 API 不包含成交量
        };
        
        info!("获取到价格数据: {} = ${}", symbol, price);
        Ok(price_data)
    }
    
    async fn get_klines(
        &self,
        symbol: &str,
        interval: &str,
        limit: Option<u32>,
    ) -> Result<Vec<KlineData>> {
        let binance_symbol = self.convert_symbol(symbol);
        let limit = limit.unwrap_or(100).min(1000); // Binance 限制最多1000条
        
        let url = format!(
            "{}/api/v3/klines?symbol={}&interval={}&limit={}",
            self.base_url, binance_symbol, interval, limit
        );
        
        debug!("获取K线数据: {} {} 条数: {}", symbol, interval, limit);
        
        let response = self.client
            .get(&url)
            .send()
            .await
            .map_err(|e| QuantGridError::Data(format!("API 请求失败: {}", e)))?;
        
        if !response.status().is_success() {
            return Err(QuantGridError::Data(
                format!("API 返回错误状态: {}", response.status())
            ));
        }
        
        let klines_response: Vec<Vec<serde_json::Value>> = response
            .json()
            .await
            .map_err(|e| QuantGridError::Data(format!("JSON 解析失败: {}", e)))?;
        
        let mut klines = Vec::new();
        
        for kline_data in klines_response {
            if kline_data.len() >= 6 {
                let open_time = kline_data[0].as_i64().unwrap_or(0);
                let open = self.parse_price(&kline_data[1].as_str().unwrap_or("0"))?;
                let high = self.parse_price(&kline_data[2].as_str().unwrap_or("0"))?;
                let low = self.parse_price(&kline_data[3].as_str().unwrap_or("0"))?;
                let close = self.parse_price(&kline_data[4].as_str().unwrap_or("0"))?;
                let volume = self.parse_price(&kline_data[5].as_str().unwrap_or("0"))?;
                
                let kline = KlineData {
                    symbol: symbol.to_string(),
                    open,
                    high,
                    low,
                    close,
                    volume,
                    timestamp: chrono::DateTime::from_timestamp(open_time / 1000, 0)
                        .unwrap_or_else(chrono::Utc::now),
                };
                
                klines.push(kline);
            }
        }
        
        info!("获取到 {} 条K线数据: {}", klines.len(), symbol);
        Ok(klines)
    }
    
    async fn subscribe_price(&self, symbol: &str) -> Result<()> {
        // TODO: 实现 WebSocket 价格订阅
        info!("订阅价格更新: {}", symbol);
        Ok(())
    }
    
    async fn unsubscribe_price(&self, symbol: &str) -> Result<()> {
        // TODO: 实现 WebSocket 价格取消订阅
        info!("取消订阅价格更新: {}", symbol);
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_binance_price() {
        let provider = BinanceMarketDataProvider::new();
        
        // 测试获取 BTC/USDT 价格
        match provider.get_price("BTC/USDT").await {
            Ok(price_data) => {
                println!("BTC/USDT 价格: ${}", price_data.price);
                assert!(price_data.price > Decimal::ZERO);
                assert_eq!(price_data.symbol, "BTC/USDT");
            }
            Err(e) => {
                println!("价格获取失败: {}", e);
                // 在网络不可用时不让测试失败
            }
        }
    }
    
    #[tokio::test]
    async fn test_binance_klines() {
        let provider = BinanceMarketDataProvider::new();
        
        // 测试获取 BNB/USDT K线数据
        match provider.get_klines("BNB/USDT", "1h", Some(10)).await {
            Ok(klines) => {
                println!("获取到 {} 条K线数据", klines.len());
                assert!(!klines.is_empty());
                assert!(klines.len() <= 10);
                
                for kline in &klines[..3.min(klines.len())] {
                    println!("K线: O:{} H:{} L:{} C:{} V:{}", 
                             kline.open, kline.high, kline.low, kline.close, kline.volume);
                }
            }
            Err(e) => {
                println!("K线获取失败: {}", e);
                // 在网络不可用时不让测试失败
            }
        }
    }
}
