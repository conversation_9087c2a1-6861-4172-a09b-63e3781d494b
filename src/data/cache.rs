//! 数据缓存模块
//! 
//! 负责数据的内存缓存管理

use crate::{info, debug};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 数据缓存
#[derive(Debug)]
pub struct DataCache<T> {
    /// 缓存数据
    data: HashMap<String, CacheEntry<T>>,
    /// 默认过期时间 (秒)
    default_ttl: u64,
}

/// 缓存条目
#[derive(Debug, <PERSON>lone)]
struct CacheEntry<T> {
    /// 数据
    data: T,
    /// 过期时间
    expires_at: chrono::DateTime<chrono::Utc>,
}

impl<T> DataCache<T>
where
    T: Clone,
{
    /// 创建新的数据缓存
    pub fn new(default_ttl: u64) -> Self {
        Self {
            data: HashMap::new(),
            default_ttl,
        }
    }
    
    /// 设置缓存数据
    pub fn set(&mut self, key: String, value: T) {
        self.set_with_ttl(key, value, self.default_ttl);
    }
    
    /// 设置缓存数据 (指定TTL)
    pub fn set_with_ttl(&mut self, key: String, value: T, ttl: u64) {
        let expires_at = chrono::Utc::now() + chrono::Duration::seconds(ttl as i64);
        let entry = CacheEntry {
            data: value,
            expires_at,
        };
        
        self.data.insert(key.clone(), entry);
        debug!("缓存数据: key={} ttl={}s", key, ttl);
    }
    
    /// 获取缓存数据
    pub fn get(&mut self, key: &str) -> Option<T> {
        if let Some(entry) = self.data.get(key) {
            if chrono::Utc::now() < entry.expires_at {
                debug!("缓存命中: key={}", key);
                return Some(entry.data.clone());
            } else {
                // 数据已过期，移除
                self.data.remove(key);
                debug!("缓存过期: key={}", key);
            }
        }
        
        debug!("缓存未命中: key={}", key);
        None
    }
    
    /// 移除缓存数据
    pub fn remove(&mut self, key: &str) -> Option<T> {
        if let Some(entry) = self.data.remove(key) {
            debug!("移除缓存: key={}", key);
            Some(entry.data)
        } else {
            None
        }
    }
    
    /// 清理过期数据
    pub fn cleanup_expired(&mut self) -> usize {
        let now = chrono::Utc::now();
        let initial_count = self.data.len();
        
        self.data.retain(|key, entry| {
            let is_valid = now < entry.expires_at;
            if !is_valid {
                debug!("清理过期缓存: key={}", key);
            }
            is_valid
        });
        
        let removed_count = initial_count - self.data.len();
        if removed_count > 0 {
            info!("清理了 {} 个过期缓存条目", removed_count);
        }
        
        removed_count
    }
    
    /// 清空所有缓存
    pub fn clear(&mut self) {
        let count = self.data.len();
        self.data.clear();
        info!("清空了 {} 个缓存条目", count);
    }
    
    /// 获取缓存统计信息
    pub fn stats(&self) -> CacheStats {
        CacheStats {
            total_entries: self.data.len(),
            default_ttl: self.default_ttl,
        }
    }
}

/// 缓存统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheStats {
    pub total_entries: usize,
    pub default_ttl: u64,
}

/// 缓存管理器
#[derive(Debug)]
pub struct CacheManager {
    /// 价格数据缓存
    price_cache: DataCache<super::market_data::PriceData>,
    /// K线数据缓存
    kline_cache: DataCache<Vec<super::market_data::KlineData>>,
}

impl CacheManager {
    /// 创建新的缓存管理器
    pub fn new() -> Self {
        Self {
            price_cache: DataCache::new(30), // 价格数据缓存30秒
            kline_cache: DataCache::new(300), // K线数据缓存5分钟
        }
    }
    
    /// 缓存价格数据
    pub fn cache_price(&mut self, symbol: &str, price_data: super::market_data::PriceData) {
        self.price_cache.set(symbol.to_string(), price_data);
    }
    
    /// 获取缓存的价格数据
    pub fn get_cached_price(&mut self, symbol: &str) -> Option<super::market_data::PriceData> {
        self.price_cache.get(symbol)
    }
    
    /// 缓存K线数据
    pub fn cache_klines(&mut self, key: &str, klines: Vec<super::market_data::KlineData>) {
        self.kline_cache.set(key.to_string(), klines);
    }
    
    /// 获取缓存的K线数据
    pub fn get_cached_klines(&mut self, key: &str) -> Option<Vec<super::market_data::KlineData>> {
        self.kline_cache.get(key)
    }
    
    /// 清理所有过期缓存
    pub fn cleanup_all_expired(&mut self) -> usize {
        let price_removed = self.price_cache.cleanup_expired();
        let kline_removed = self.kline_cache.cleanup_expired();
        price_removed + kline_removed
    }
    
    /// 清空所有缓存
    pub fn clear_all(&mut self) {
        self.price_cache.clear();
        self.kline_cache.clear();
    }
    
    /// 获取缓存统计信息
    pub fn get_stats(&self) -> CacheManagerStats {
        CacheManagerStats {
            price_cache: self.price_cache.stats(),
            kline_cache: self.kline_cache.stats(),
        }
    }
}

impl Default for CacheManager {
    fn default() -> Self {
        Self::new()
    }
}

/// 缓存管理器统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheManagerStats {
    pub price_cache: CacheStats,
    pub kline_cache: CacheStats,
}
