//! 市场数据获取模块
//! 
//! 负责从各种数据源获取实时市场数据

use crate::{Result, QuantGridError, info, debug};
use rust_decimal::Decimal;
use rust_decimal::prelude::*;
use serde::{Deserialize, Serialize};

/// 价格数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PriceData {
    /// 交易对符号
    pub symbol: String,
    /// 价格
    pub price: Decimal,
    /// 时间戳
    pub timestamp: chrono::DateTime<chrono::Utc>,
    /// 数据源
    pub source: String,
}

/// K线数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct KlineData {
    /// 交易对符号
    pub symbol: String,
    /// 开盘价
    pub open: Decimal,
    /// 最高价
    pub high: Decimal,
    /// 最低价
    pub low: Decimal,
    /// 收盘价
    pub close: Decimal,
    /// 成交量
    pub volume: Decimal,
    /// 开始时间
    pub start_time: chrono::DateTime<chrono::Utc>,
    /// 结束时间
    pub end_time: chrono::DateTime<chrono::Utc>,
    /// 时间间隔 (1m, 5m, 1h, 1d等)
    pub interval: String,
}

/// 市场数据提供者trait
pub trait MarketDataProvider {
    /// 获取实时价格
    async fn get_price(&self, symbol: &str) -> Result<PriceData>;
    
    /// 获取K线数据
    async fn get_klines(
        &self,
        symbol: &str,
        interval: &str,
        limit: Option<u32>,
    ) -> Result<Vec<KlineData>>;
    
    /// 订阅价格更新
    async fn subscribe_price(&self, symbol: &str) -> Result<()>;
    
    /// 取消订阅
    async fn unsubscribe_price(&self, symbol: &str) -> Result<()>;
}

/// 模拟市场数据提供者 (用于测试)
#[derive(Debug)]
pub struct MockMarketDataProvider {
    /// 模拟价格数据
    mock_prices: std::collections::HashMap<String, Decimal>,
}

impl MockMarketDataProvider {
    /// 创建新的模拟数据提供者
    pub fn new() -> Self {
        let mut mock_prices = std::collections::HashMap::new();
        
        // 添加一些模拟价格
        mock_prices.insert("BNB/USDT".to_string(), Decimal::from(300));
        mock_prices.insert("ETH/USDT".to_string(), Decimal::from(2000));
        mock_prices.insert("BTC/USDT".to_string(), Decimal::from(45000));
        
        Self { mock_prices }
    }
    
    /// 设置模拟价格
    pub fn set_price(&mut self, symbol: &str, price: Decimal) {
        self.mock_prices.insert(symbol.to_string(), price);
    }
}

impl Default for MockMarketDataProvider {
    fn default() -> Self {
        Self::new()
    }
}

impl MarketDataProvider for MockMarketDataProvider {
    async fn get_price(&self, symbol: &str) -> Result<PriceData> {
        if let Some(&price) = self.mock_prices.get(symbol) {
            // 添加一些随机波动
            let volatility = Decimal::from_f64(0.001).unwrap(); // 0.1% 波动
            let random_factor = Decimal::from_f64(
                (rand::random::<f64>() - 0.5) * 2.0
            ).unwrap();
            let adjusted_price = price * (Decimal::ONE + volatility * random_factor);
            
            Ok(PriceData {
                symbol: symbol.to_string(),
                price: adjusted_price,
                timestamp: chrono::Utc::now(),
                source: "mock".to_string(),
            })
        } else {
            Err(QuantGridError::Trading(format!("未找到交易对: {}", symbol)))
        }
    }
    
    async fn get_klines(
        &self,
        symbol: &str,
        interval: &str,
        limit: Option<u32>,
    ) -> Result<Vec<KlineData>> {
        let base_price = if let Some(&price) = self.mock_prices.get(symbol) {
            price
        } else {
            return Err(QuantGridError::Trading(format!("未找到交易对: {}", symbol)));
        };
        
        let limit = limit.unwrap_or(100);
        let mut klines = Vec::new();
        
        // 生成模拟K线数据
        for i in 0..limit {
            let start_time = chrono::Utc::now() - chrono::Duration::minutes((limit - i) as i64);
            let end_time = start_time + chrono::Duration::minutes(1);
            
            // 生成随机OHLC数据
            let volatility = Decimal::from_f64(0.01).unwrap(); // 1% 波动
            let random_open = base_price * (Decimal::ONE + 
                Decimal::from_f64((rand::random::<f64>() - 0.5) * 2.0).unwrap() * volatility);
            let random_high = random_open * (Decimal::ONE + 
                Decimal::from_f64(rand::random::<f64>()).unwrap() * volatility);
            let random_low = random_open * (Decimal::ONE - 
                Decimal::from_f64(rand::random::<f64>()).unwrap() * volatility);
            let random_close = random_low + 
                (random_high - random_low) * Decimal::from_f64(rand::random::<f64>()).unwrap();
            
            klines.push(KlineData {
                symbol: symbol.to_string(),
                open: random_open,
                high: random_high,
                low: random_low,
                close: random_close,
                volume: Decimal::from(rand::random::<u32>() % 10000 + 1000),
                start_time,
                end_time,
                interval: interval.to_string(),
            });
        }
        
        debug!("生成 {} 条模拟K线数据，交易对: {}", klines.len(), symbol);
        Ok(klines)
    }
    
    async fn subscribe_price(&self, symbol: &str) -> Result<()> {
        info!("订阅价格更新: {}", symbol);
        // 模拟实现，实际应该建立WebSocket连接
        Ok(())
    }
    
    async fn unsubscribe_price(&self, symbol: &str) -> Result<()> {
        info!("取消订阅价格更新: {}", symbol);
        // 模拟实现
        Ok(())
    }
}
