//! 配置管理模块
//! 
//! 负责加载和管理系统配置

use crate::{Result, QuantGridError};
use serde::{Deserialize, Serialize};
use std::path::Path;

/// 系统配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Config {
    /// 应用配置
    pub app: AppConfig,
    /// 数据库配置
    pub database: DatabaseConfig,
    /// 区块链配置
    pub blockchain: BlockchainConfig,
    /// 交易配置
    pub trading: TradingConfig,
    /// 安全配置
    pub security: SecurityConfig,
    /// 日志配置
    pub logging: LoggingConfig,
}

/// 应用配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppConfig {
    /// 应用名称
    pub name: String,
    /// 版本
    pub version: String,
    /// 环境 (development, production)
    pub environment: String,
    /// 数据目录
    pub data_dir: String,
}

/// 数据库配置
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct DatabaseConfig {
    /// 数据库文件路径
    pub path: String,
    /// 连接池大小
    pub pool_size: u32,
    /// 连接超时 (秒)
    pub connection_timeout: u64,
}

/// 区块链配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BlockchainConfig {
    /// BSC配置
    pub bsc: BscConfig,
}

/// BSC链配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BscConfig {
    /// RPC端点
    pub rpc_url: String,
    /// WebSocket端点
    pub ws_url: String,
    /// 链ID
    pub chain_id: u64,
    /// Gas价格 (Gwei)
    pub gas_price: u64,
    /// Gas限制
    pub gas_limit: u64,
    /// 确认块数
    pub confirmations: u64,
}

/// 交易配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradingConfig {
    /// 默认滑点容忍度
    pub default_slippage: f64,
    /// 最大滑点容忍度
    pub max_slippage: f64,
    /// 订单检查间隔 (秒)
    pub order_check_interval: u64,
    /// 价格更新间隔 (秒)
    pub price_update_interval: u64,
    /// 最小交易金额 (USDT)
    pub min_trade_amount: f64,
    /// 最大并发交易数
    pub max_concurrent_trades: u32,
}

/// 安全配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityConfig {
    /// 加密密钥长度
    pub encryption_key_length: usize,
    /// 密码哈希轮数
    pub password_hash_rounds: u32,
    /// 会话超时 (分钟)
    pub session_timeout: u64,
    /// 最大登录尝试次数
    pub max_login_attempts: u32,
}

/// 日志配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoggingConfig {
    /// 日志级别
    pub level: String,
    /// 日志文件路径
    pub file_path: String,
    /// 最大文件大小 (MB)
    pub max_file_size: u64,
    /// 保留文件数量
    pub max_files: u32,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            app: AppConfig {
                name: "Quant Grid RS".to_string(),
                version: env!("CARGO_PKG_VERSION").to_string(),
                environment: "development".to_string(),
                data_dir: "./data".to_string(),
            },
            database: DatabaseConfig {
                path: "./data/quant_grid.db".to_string(),
                pool_size: 10,
                connection_timeout: 30,
            },
            blockchain: BlockchainConfig {
                bsc: BscConfig {
                    rpc_url: "https://bsc-dataseed1.binance.org/".to_string(),
                    ws_url: "wss://bsc-ws-node.nariox.org:443/".to_string(),
                    chain_id: 56,
                    gas_price: 5, // 5 Gwei
                    gas_limit: 300000,
                    confirmations: 3,
                },
            },
            trading: TradingConfig {
                default_slippage: 0.005, // 0.5%
                max_slippage: 0.02,      // 2%
                order_check_interval: 3,
                price_update_interval: 5,
                min_trade_amount: 10.0,
                max_concurrent_trades: 10,
            },
            security: SecurityConfig {
                encryption_key_length: 32,
                password_hash_rounds: 100000,
                session_timeout: 60, // 1小时
                max_login_attempts: 5,
            },
            logging: LoggingConfig {
                level: "info".to_string(),
                file_path: "./logs/quant_grid.log".to_string(),
                max_file_size: 100, // 100MB
                max_files: 10,
            },
        }
    }
}

impl Config {
    /// 加载配置文件
    pub fn load() -> Result<Self> {
        let config_path = "config.toml";
        
        if Path::new(config_path).exists() {
            Self::load_from_file(config_path)
        } else {
            // 如果配置文件不存在，创建默认配置文件
            let default_config = Self::default();
            default_config.save_to_file(config_path)?;
            Ok(default_config)
        }
    }
    
    /// 从文件加载配置
    pub fn load_from_file<P: AsRef<Path>>(path: P) -> Result<Self> {
        let content = std::fs::read_to_string(path)
            .map_err(|e| QuantGridError::Config(format!("读取配置文件失败: {}", e)))?;

        let config: Config = toml::from_str(&content)
            .map_err(|e| QuantGridError::Config(format!("解析配置文件失败: {}", e)))?;

        // 验证配置
        config.validate()?;

        Ok(config)
    }
    
    /// 保存配置到文件
    pub fn save_to_file<P: AsRef<Path>>(&self, path: P) -> Result<()> {
        let content = toml::to_string_pretty(self)
            .map_err(|e| QuantGridError::Config(format!("序列化配置失败: {}", e)))?;
        
        // 确保目录存在
        if let Some(parent) = path.as_ref().parent() {
            std::fs::create_dir_all(parent)
                .map_err(|e| QuantGridError::Config(format!("创建配置目录失败: {}", e)))?;
        }
        
        std::fs::write(path, content)
            .map_err(|e| QuantGridError::Config(format!("写入配置文件失败: {}", e)))?;
        
        Ok(())
    }
    
    /// 验证配置
    fn validate(&self) -> Result<()> {
        // 验证数据库配置
        if self.database.pool_size == 0 {
            return Err(QuantGridError::Config("数据库连接池大小不能为0".to_string()));
        }
        
        // 验证区块链配置
        if self.blockchain.bsc.rpc_url.is_empty() {
            return Err(QuantGridError::Config("BSC RPC URL不能为空".to_string()));
        }
        
        if self.blockchain.bsc.chain_id == 0 {
            return Err(QuantGridError::Config("BSC链ID不能为0".to_string()));
        }
        
        // 验证交易配置
        if self.trading.default_slippage < 0.0 || self.trading.default_slippage > 1.0 {
            return Err(QuantGridError::Config("默认滑点必须在0-100%之间".to_string()));
        }
        
        if self.trading.max_slippage < self.trading.default_slippage {
            return Err(QuantGridError::Config("最大滑点不能小于默认滑点".to_string()));
        }
        
        if self.trading.min_trade_amount <= 0.0 {
            return Err(QuantGridError::Config("最小交易金额必须大于0".to_string()));
        }
        
        // 验证安全配置
        if self.security.encryption_key_length < 16 {
            return Err(QuantGridError::Config("加密密钥长度不能小于16字节".to_string()));
        }
        
        if self.security.password_hash_rounds < 10000 {
            return Err(QuantGridError::Config("密码哈希轮数不能小于10000".to_string()));
        }
        
        Ok(())
    }
    
    /// 获取数据目录路径
    pub fn get_data_dir(&self) -> &str {
        &self.app.data_dir
    }
    
    /// 获取日志目录路径
    pub fn get_log_dir(&self) -> String {
        if let Some(parent) = Path::new(&self.logging.file_path).parent() {
            parent.to_string_lossy().to_string()
        } else {
            "./logs".to_string()
        }
    }
    
    /// 确保必要的目录存在
    pub fn ensure_directories(&self) -> Result<()> {
        // 创建数据目录
        std::fs::create_dir_all(&self.app.data_dir)
            .map_err(|e| QuantGridError::Config(format!("创建数据目录失败: {}", e)))?;
        
        // 创建日志目录
        let log_dir = self.get_log_dir();
        std::fs::create_dir_all(&log_dir)
            .map_err(|e| QuantGridError::Config(format!("创建日志目录失败: {}", e)))?;
        
        Ok(())
    }
}
