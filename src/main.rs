use quant_grid_rs::{
    utils::{config::Config, logger::init_logger},
    Result, info,
};

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志系统
    init_logger()?;

    // 加载配置
    let config = Config::load()?;

    info!("🚀 Quant Grid RS 量化交易系统启动中...");
    info!("版本: {}", env!("CARGO_PKG_VERSION"));
    info!("配置文件: {:?}", config);

    // TODO: 初始化各个模块
    // - 数据库连接
    // - 区块链客户端
    // - 交易引擎
    // - Web界面 (Tauri)

    info!("✅ 系统启动完成");

    // 保持程序运行
    tokio::signal::ctrl_c().await.expect("Failed to listen for ctrl+c");
    info!("👋 系统正在关闭...");

    Ok(())
}
