//! DEX集成模块
//! 
//! 支持多个去中心化交易所

pub mod pancakeswap;

// 重新导出主要类型
pub use pancakeswap::PancakeSwapClient;

/// DEX客户端trait
pub trait DexClient {
    /// 获取交易对价格
    async fn get_price(&self, symbol: &str) -> crate::Result<rust_decimal::Decimal>;
    
    /// 执行交易
    async fn execute_trade(
        &self,
        symbol: &str,
        amount: rust_decimal::Decimal,
        is_buy: bool,
    ) -> crate::Result<String>;
    
    /// 获取交易对信息
    async fn get_pair_info(&self, symbol: &str) -> crate::Result<PairInfo>;
}

/// 交易对信息
#[derive(Debug, Clone)]
pub struct PairInfo {
    pub symbol: String,
    pub base_token: String,
    pub quote_token: String,
    pub liquidity: rust_decimal::Decimal,
    pub volume_24h: rust_decimal::Decimal,
}
