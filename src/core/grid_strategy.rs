//! 动态网格策略实现
//! 
//! 根据市场波动性自动调整网格间距，实现智能化的网格交易策略

use crate::{Result, QuantGridError};
use rust_decimal::Decimal;
use rust_decimal::prelude::*;
use serde::{Deserialize, Serialize};
use std::collections::VecDeque;
use crate::{info, debug, warn};

/// 网格配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GridConfig {
    /// 交易对符号 (例如: "BNB/USDT")
    pub symbol: String,
    /// 网格数量
    pub grid_count: u32,
    /// 价格区间下限
    pub price_lower: Decimal,
    /// 价格区间上限
    pub price_upper: Decimal,
    /// 单笔交易金额 (USDT)
    pub trade_amount: Decimal,
    /// 是否启用动态调整
    pub dynamic_adjustment: bool,
    /// 波动性检测周期 (分钟)
    pub volatility_period: u32,
    /// 动态调整阈值
    pub adjustment_threshold: Decimal,
}

/// 网格订单
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GridOrder {
    /// 订单ID
    pub id: String,
    /// 网格层级
    pub grid_level: u32,
    /// 订单类型 (买入/卖出)
    pub order_type: OrderType,
    /// 目标价格
    pub price: Decimal,
    /// 交易数量
    pub quantity: Decimal,
    /// 订单状态
    pub status: OrderStatus,
    /// 创建时间
    pub created_at: chrono::DateTime<chrono::Utc>,
}

/// 订单类型
#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq)]
pub enum OrderType {
    Buy,
    Sell,
}

/// 订单状态
#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq)]
pub enum OrderStatus {
    Pending,    // 等待执行
    Filled,     // 已成交
    Cancelled,  // 已取消
    Failed,     // 执行失败
}

/// 动态网格策略
#[derive(Debug)]
pub struct GridStrategy {
    /// 配置参数
    config: GridConfig,
    /// 当前网格订单
    grid_orders: Vec<GridOrder>,
    /// 价格历史 (用于计算波动性)
    price_history: VecDeque<(chrono::DateTime<chrono::Utc>, Decimal)>,
    /// 当前市场价格
    current_price: Option<Decimal>,
    /// 网格间距
    grid_spacing: Decimal,
    /// 是否已初始化
    initialized: bool,
}

impl GridStrategy {
    /// 创建新的网格策略实例
    pub fn new(config: GridConfig) -> Result<Self> {
        // 验证配置参数
        if config.grid_count == 0 {
            return Err(QuantGridError::Config("网格数量不能为0".to_string()));
        }
        
        if config.price_upper <= config.price_lower {
            return Err(QuantGridError::Config("价格上限必须大于下限".to_string()));
        }
        
        if config.trade_amount <= Decimal::ZERO {
            return Err(QuantGridError::Config("交易金额必须大于0".to_string()));
        }
        
        // 计算初始网格间距
        let price_range = config.price_upper - config.price_lower;
        let grid_spacing = price_range / Decimal::from(config.grid_count);
        
        info!("创建网格策略: {} 网格数量: {} 价格区间: {}-{} 网格间距: {}", 
              config.symbol, config.grid_count, config.price_lower, config.price_upper, grid_spacing);
        
        Ok(Self {
            config,
            grid_orders: Vec::new(),
            price_history: VecDeque::new(),
            current_price: None,
            grid_spacing,
            initialized: false,
        })
    }
    
    /// 初始化网格订单
    pub fn initialize(&mut self, current_price: Decimal) -> Result<()> {
        if self.initialized {
            warn!("网格策略已经初始化");
            return Ok(());
        }
        
        // 检查当前价格是否在配置的价格区间内
        if current_price < self.config.price_lower || current_price > self.config.price_upper {
            return Err(QuantGridError::Config(
                format!("当前价格 {} 不在配置的价格区间 {}-{} 内", 
                        current_price, self.config.price_lower, self.config.price_upper)
            ));
        }
        
        self.current_price = Some(current_price);
        self.generate_initial_grid_orders(current_price)?;
        self.initialized = true;
        
        info!("网格策略初始化完成，生成 {} 个订单", self.grid_orders.len());
        Ok(())
    }
    
    /// 生成初始网格订单
    fn generate_initial_grid_orders(&mut self, current_price: Decimal) -> Result<()> {
        self.grid_orders.clear();
        
        // 计算当前价格所在的网格层级
        let current_level = ((current_price - self.config.price_lower) / self.grid_spacing).floor();
        let current_level = current_level.to_u32().unwrap_or(0);
        
        // 生成买入订单 (当前价格以下)
        for level in 0..current_level {
            let price = self.config.price_lower + self.grid_spacing * Decimal::from(level);
            let order = self.create_grid_order(level, OrderType::Buy, price)?;
            self.grid_orders.push(order);
        }
        
        // 生成卖出订单 (当前价格以上)
        for level in (current_level + 1)..self.config.grid_count {
            let price = self.config.price_lower + self.grid_spacing * Decimal::from(level);
            let order = self.create_grid_order(level, OrderType::Sell, price)?;
            self.grid_orders.push(order);
        }
        
        debug!("生成 {} 个买入订单，{} 个卖出订单", 
               self.grid_orders.iter().filter(|o| o.order_type == OrderType::Buy).count(),
               self.grid_orders.iter().filter(|o| o.order_type == OrderType::Sell).count());
        
        Ok(())
    }
    
    /// 创建网格订单
    fn create_grid_order(&self, level: u32, order_type: OrderType, price: Decimal) -> Result<GridOrder> {
        let id = format!("{}_{}_{}_{}", self.config.symbol, level, 
                        match order_type { OrderType::Buy => "BUY", OrderType::Sell => "SELL" },
                        chrono::Utc::now().timestamp_millis());
        
        // 根据价格计算交易数量
        let quantity = self.config.trade_amount / price;
        
        Ok(GridOrder {
            id,
            grid_level: level,
            order_type,
            price,
            quantity,
            status: OrderStatus::Pending,
            created_at: chrono::Utc::now(),
        })
    }
    
    /// 更新市场价格
    pub fn update_price(&mut self, price: Decimal) -> Result<Vec<String>> {
        self.current_price = Some(price);
        
        // 记录价格历史
        self.price_history.push_back((chrono::Utc::now(), price));
        
        // 保持价格历史在合理范围内
        let max_history = (self.config.volatility_period * 60) as usize; // 转换为秒
        while self.price_history.len() > max_history {
            self.price_history.pop_front();
        }
        
        // 检查是否需要触发订单
        let triggered_orders = self.check_triggered_orders(price);
        
        // 如果启用动态调整，检查是否需要调整网格
        if self.config.dynamic_adjustment {
            self.check_dynamic_adjustment()?;
        }
        
        Ok(triggered_orders)
    }
    
    /// 检查被触发的订单
    fn check_triggered_orders(&mut self, current_price: Decimal) -> Vec<String> {
        let mut triggered = Vec::new();
        
        for order in &mut self.grid_orders {
            if order.status != OrderStatus::Pending {
                continue;
            }
            
            let should_trigger = match order.order_type {
                OrderType::Buy => current_price <= order.price,
                OrderType::Sell => current_price >= order.price,
            };
            
            if should_trigger {
                debug!("触发订单: {} 类型: {:?} 价格: {} 当前价格: {}", 
                       order.id, order.order_type, order.price, current_price);
                triggered.push(order.id.clone());
                // 注意：这里只是标记为触发，实际执行由交易引擎处理
            }
        }
        
        triggered
    }
    
    /// 检查是否需要动态调整网格
    fn check_dynamic_adjustment(&mut self) -> Result<()> {
        if self.price_history.len() < 2 {
            return Ok(());
        }
        
        // 计算价格波动性
        let volatility = self.calculate_volatility()?;
        
        // 如果波动性超过阈值，调整网格间距
        if volatility > self.config.adjustment_threshold {
            info!("检测到高波动性 {:.4}，调整网格间距", volatility);
            self.adjust_grid_spacing(volatility)?;
        }
        
        Ok(())
    }
    
    /// 计算价格波动性 (标准差)
    fn calculate_volatility(&self) -> Result<Decimal> {
        if self.price_history.len() < 2 {
            return Ok(Decimal::ZERO);
        }
        
        let prices: Vec<Decimal> = self.price_history.iter().map(|(_, price)| *price).collect();
        let mean = prices.iter().sum::<Decimal>() / Decimal::from(prices.len());
        
        let variance = prices.iter()
            .map(|price| (*price - mean).powi(2))
            .sum::<Decimal>() / Decimal::from(prices.len());
        
        // 简化的标准差计算 (实际应用中可能需要更精确的计算)
        Ok(variance.sqrt().unwrap_or(Decimal::ZERO))
    }
    
    /// 调整网格间距
    fn adjust_grid_spacing(&mut self, volatility: Decimal) -> Result<()> {
        // 根据波动性调整网格间距
        let adjustment_factor = Decimal::ONE + (volatility / Decimal::from(10));
        let new_spacing = self.grid_spacing * adjustment_factor;
        
        info!("调整网格间距: {} -> {} (调整因子: {})", 
              self.grid_spacing, new_spacing, adjustment_factor);
        
        self.grid_spacing = new_spacing;
        
        // 重新生成网格订单
        if let Some(current_price) = self.current_price {
            self.generate_initial_grid_orders(current_price)?;
        }
        
        Ok(())
    }
    
    /// 标记订单为已成交
    pub fn mark_order_filled(&mut self, order_id: &str) -> Result<()> {
        if let Some(order) = self.grid_orders.iter_mut().find(|o| o.id == order_id) {
            order.status = OrderStatus::Filled;
            info!("订单已成交: {} 类型: {:?} 价格: {}", order.id, order.order_type, order.price);
            
            // 生成对应的反向订单
            self.generate_reverse_order(order)?;
        }
        
        Ok(())
    }
    
    /// 生成反向订单 (买入后生成卖出，卖出后生成买入)
    fn generate_reverse_order(&mut self, filled_order: &GridOrder) -> Result<()> {
        let reverse_type = match filled_order.order_type {
            OrderType::Buy => OrderType::Sell,
            OrderType::Sell => OrderType::Buy,
        };
        
        let reverse_price = match reverse_type {
            OrderType::Buy => filled_order.price - self.grid_spacing,
            OrderType::Sell => filled_order.price + self.grid_spacing,
        };
        
        // 检查反向价格是否在有效范围内
        if reverse_price >= self.config.price_lower && reverse_price <= self.config.price_upper {
            let reverse_order = self.create_grid_order(
                filled_order.grid_level, 
                reverse_type, 
                reverse_price
            )?;
            
            self.grid_orders.push(reverse_order);
            debug!("生成反向订单: 类型: {:?} 价格: {}", reverse_type, reverse_price);
        }
        
        Ok(())
    }
    
    /// 获取当前网格订单
    pub fn get_grid_orders(&self) -> &[GridOrder] {
        &self.grid_orders
    }
    
    /// 获取配置
    pub fn get_config(&self) -> &GridConfig {
        &self.config
    }
    
    /// 获取当前价格
    pub fn get_current_price(&self) -> Option<Decimal> {
        self.current_price
    }
    
    /// 获取网格间距
    pub fn get_grid_spacing(&self) -> Decimal {
        self.grid_spacing
    }
    
    /// 是否已初始化
    pub fn is_initialized(&self) -> bool {
        self.initialized
    }
}
