//! 风险管理模块
//! 
//! 负责交易风险控制和资金管理

use crate::{Result, QuantGridError};
use rust_decimal::Decimal;
use rust_decimal::prelude::*;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use crate::{info, debug, warn, error};

/// 风险配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskConfig {
    /// 最大单笔交易金额 (USDT)
    pub max_trade_amount: Decimal,
    /// 最大日交易金额 (USDT)
    pub max_daily_amount: Decimal,
    /// 最大持仓比例 (0-1)
    pub max_position_ratio: Decimal,
    /// 最大滑点容忍度 (0-1)
    pub max_slippage: Decimal,
    /// 最大连续亏损次数
    pub max_consecutive_losses: u32,
    /// 止损比例 (0-1)
    pub stop_loss_ratio: Decimal,
    /// 是否启用风险控制
    pub enabled: bool,
}

impl Default for RiskConfig {
    fn default() -> Self {
        Self {
            max_trade_amount: Decimal::from(1000), // 1000 USDT
            max_daily_amount: Decimal::from(10000), // 10000 USDT
            max_position_ratio: Decimal::from_f64(0.8).unwrap(), // 80%
            max_slippage: Decimal::from_f64(0.02).unwrap(), // 2%
            max_consecutive_losses: 5,
            stop_loss_ratio: Decimal::from_f64(0.05).unwrap(), // 5%
            enabled: true,
        }
    }
}

/// 风险统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskStats {
    /// 今日交易金额
    pub daily_trade_amount: Decimal,
    /// 当前持仓价值
    pub current_position_value: Decimal,
    /// 总资产价值
    pub total_asset_value: Decimal,
    /// 连续亏损次数
    pub consecutive_losses: u32,
    /// 今日盈亏
    pub daily_pnl: Decimal,
    /// 总盈亏
    pub total_pnl: Decimal,
}

impl Default for RiskStats {
    fn default() -> Self {
        Self {
            daily_trade_amount: Decimal::ZERO,
            current_position_value: Decimal::ZERO,
            total_asset_value: Decimal::ZERO,
            consecutive_losses: 0,
            daily_pnl: Decimal::ZERO,
            total_pnl: Decimal::ZERO,
        }
    }
}

/// 风险检查结果
#[derive(Debug, Clone)]
pub struct RiskCheckResult {
    /// 是否通过风险检查
    pub passed: bool,
    /// 风险警告信息
    pub warnings: Vec<String>,
    /// 风险错误信息
    pub errors: Vec<String>,
}

/// 风险管理器
#[derive(Debug)]
pub struct RiskManager {
    /// 风险配置
    config: RiskConfig,
    /// 风险统计
    stats: RiskStats,
    /// 每日交易记录 (日期 -> 交易金额)
    daily_trades: HashMap<String, Decimal>,
    /// 交易历史 (用于计算连续亏损)
    trade_history: Vec<TradeRecord>,
}

/// 交易记录
#[derive(Debug, Clone)]
struct TradeRecord {
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub pnl: Decimal,
    pub is_profit: bool,
}

impl RiskManager {
    /// 创建新的风险管理器
    pub fn new(config: RiskConfig) -> Self {
        info!("创建风险管理器，配置: {:?}", config);
        
        Self {
            config,
            stats: RiskStats::default(),
            daily_trades: HashMap::new(),
            trade_history: Vec::new(),
        }
    }
    
    /// 使用默认配置创建风险管理器
    pub fn with_default_config() -> Self {
        Self::new(RiskConfig::default())
    }
    
    /// 检查交易风险
    pub fn check_trade_risk(
        &self,
        trade_amount: Decimal,
        current_price: Decimal,
        target_price: Decimal,
    ) -> RiskCheckResult {
        let mut result = RiskCheckResult {
            passed: true,
            warnings: Vec::new(),
            errors: Vec::new(),
        };
        
        if !self.config.enabled {
            result.warnings.push("风险控制已禁用".to_string());
            return result;
        }
        
        // 检查单笔交易金额
        if trade_amount > self.config.max_trade_amount {
            result.passed = false;
            result.errors.push(format!(
                "单笔交易金额 {} 超过限制 {}",
                trade_amount, self.config.max_trade_amount
            ));
        }
        
        // 检查日交易金额
        let today = chrono::Utc::now().format("%Y-%m-%d").to_string();
        let daily_amount = self.daily_trades.get(&today).unwrap_or(&Decimal::ZERO) + trade_amount;
        if daily_amount > self.config.max_daily_amount {
            result.passed = false;
            result.errors.push(format!(
                "日交易金额 {} 超过限制 {}",
                daily_amount, self.config.max_daily_amount
            ));
        }
        
        // 检查滑点
        let slippage = if current_price > Decimal::ZERO {
            (target_price - current_price).abs() / current_price
        } else {
            Decimal::ZERO
        };
        
        if slippage > self.config.max_slippage {
            result.passed = false;
            result.errors.push(format!(
                "滑点 {:.4} 超过限制 {:.4}",
                slippage, self.config.max_slippage
            ));
        } else if slippage > self.config.max_slippage * Decimal::from_f64(0.8).unwrap() {
            result.warnings.push(format!(
                "滑点 {:.4} 接近限制 {:.4}",
                slippage, self.config.max_slippage
            ));
        }
        
        // 检查持仓比例
        let new_position_value = self.stats.current_position_value + trade_amount;
        if self.stats.total_asset_value > Decimal::ZERO {
            let position_ratio = new_position_value / self.stats.total_asset_value;
            if position_ratio > self.config.max_position_ratio {
                result.passed = false;
                result.errors.push(format!(
                    "持仓比例 {:.4} 超过限制 {:.4}",
                    position_ratio, self.config.max_position_ratio
                ));
            }
        }
        
        // 检查连续亏损
        if self.stats.consecutive_losses >= self.config.max_consecutive_losses {
            result.passed = false;
            result.errors.push(format!(
                "连续亏损次数 {} 达到限制 {}",
                self.stats.consecutive_losses, self.config.max_consecutive_losses
            ));
        }
        
        // 检查止损
        if self.stats.total_pnl < -self.stats.total_asset_value * self.config.stop_loss_ratio {
            result.passed = false;
            result.errors.push(format!(
                "总亏损 {} 触发止损线 {}",
                self.stats.total_pnl,
                -self.stats.total_asset_value * self.config.stop_loss_ratio
            ));
        }
        
        if !result.passed {
            error!("风险检查失败: {:?}", result.errors);
        } else if !result.warnings.is_empty() {
            warn!("风险检查警告: {:?}", result.warnings);
        } else {
            debug!("风险检查通过");
        }
        
        result
    }
    
    /// 记录交易
    pub fn record_trade(&mut self, trade_amount: Decimal, pnl: Decimal) {
        let today = chrono::Utc::now().format("%Y-%m-%d").to_string();
        
        // 更新日交易金额
        let daily_amount = self.daily_trades.get(&today).unwrap_or(&Decimal::ZERO) + trade_amount;
        self.daily_trades.insert(today, daily_amount);
        
        // 更新统计信息
        self.stats.daily_trade_amount = daily_amount;
        self.stats.daily_pnl += pnl;
        self.stats.total_pnl += pnl;
        
        // 记录交易历史
        let trade_record = TradeRecord {
            timestamp: chrono::Utc::now(),
            pnl,
            is_profit: pnl > Decimal::ZERO,
        };
        
        self.trade_history.push(trade_record);
        
        // 保持历史记录在合理范围内
        if self.trade_history.len() > 1000 {
            self.trade_history.remove(0);
        }
        
        // 更新连续亏损次数
        self.update_consecutive_losses();
        
        info!("记录交易: 金额={} 盈亏={} 连续亏损={}", 
              trade_amount, pnl, self.stats.consecutive_losses);
    }
    
    /// 更新连续亏损次数
    fn update_consecutive_losses(&mut self) {
        let mut consecutive_losses = 0;
        
        // 从最新的交易开始倒序计算连续亏损
        for trade in self.trade_history.iter().rev() {
            if trade.is_profit {
                break;
            }
            consecutive_losses += 1;
        }
        
        self.stats.consecutive_losses = consecutive_losses;
    }
    
    /// 更新资产信息
    pub fn update_assets(&mut self, position_value: Decimal, total_value: Decimal) {
        self.stats.current_position_value = position_value;
        self.stats.total_asset_value = total_value;
        
        debug!("更新资产信息: 持仓价值={} 总资产={}", position_value, total_value);
    }
    
    /// 重置日统计
    pub fn reset_daily_stats(&mut self) {
        let today = chrono::Utc::now().format("%Y-%m-%d").to_string();
        self.daily_trades.insert(today, Decimal::ZERO);
        self.stats.daily_trade_amount = Decimal::ZERO;
        self.stats.daily_pnl = Decimal::ZERO;
        
        info!("重置日统计");
    }
    
    /// 获取风险配置
    pub fn get_config(&self) -> &RiskConfig {
        &self.config
    }
    
    /// 更新风险配置
    pub fn update_config(&mut self, config: RiskConfig) {
        info!("更新风险配置: {:?}", config);
        self.config = config;
    }
    
    /// 获取风险统计
    pub fn get_stats(&self) -> &RiskStats {
        &self.stats
    }
    
    /// 启用风险控制
    pub fn enable(&mut self) {
        self.config.enabled = true;
        info!("风险控制已启用");
    }
    
    /// 禁用风险控制
    pub fn disable(&mut self) {
        self.config.enabled = false;
        warn!("风险控制已禁用");
    }
    
    /// 是否启用风险控制
    pub fn is_enabled(&self) -> bool {
        self.config.enabled
    }
}
