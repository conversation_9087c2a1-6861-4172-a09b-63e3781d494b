//! 虚拟订单簿实现
//! 
//! 由于DEX没有中心化订单簿，系统需维护本地虚拟订单簿
//! 当市场价格达到虚拟订单价格时，自动执行链上交易

use crate::{Result, QuantGridError};
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use std::collections::{BTreeMap, HashMap};
use crate::{info, debug, warn};

// 重新导出订单类型和状态
pub use super::grid_strategy::{OrderType, OrderStatus};

/// 虚拟订单
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VirtualOrder {
    /// 订单ID
    pub id: String,
    /// 交易对符号
    pub symbol: String,
    /// 订单类型
    pub order_type: OrderType,
    /// 目标价格
    pub price: Decimal,
    /// 交易数量
    pub quantity: Decimal,
    /// 订单状态
    pub status: OrderStatus,
    /// 创建时间
    pub created_at: chrono::DateTime<chrono::Utc>,
    /// 更新时间
    pub updated_at: chrono::DateTime<chrono::Utc>,
    /// 关联的网格层级 (可选)
    pub grid_level: Option<u32>,
    /// 滑点容忍度
    pub slippage_tolerance: Decimal,
}

/// 订单簿条目
#[derive(Debug, Clone)]
struct OrderBookEntry {
    price: Decimal,
    orders: Vec<VirtualOrder>,
}

/// 虚拟订单簿
#[derive(Debug)]
pub struct VirtualOrderBook {
    /// 交易对符号
    symbol: String,
    /// 买入订单 (价格从高到低排序)
    buy_orders: BTreeMap<String, VirtualOrder>, // 使用价格的字符串表示作为key以保持排序
    /// 卖出订单 (价格从低到高排序)
    sell_orders: BTreeMap<String, VirtualOrder>,
    /// 订单ID到订单的映射
    order_map: HashMap<String, VirtualOrder>,
    /// 当前市场价格
    current_price: Option<Decimal>,
}

impl VirtualOrderBook {
    /// 创建新的虚拟订单簿
    pub fn new(symbol: String) -> Self {
        info!("创建虚拟订单簿: {}", symbol);
        
        Self {
            symbol,
            buy_orders: BTreeMap::new(),
            sell_orders: BTreeMap::new(),
            order_map: HashMap::new(),
            current_price: None,
        }
    }
    
    /// 添加订单到订单簿
    pub fn add_order(&mut self, mut order: VirtualOrder) -> Result<()> {
        // 验证订单
        if order.symbol != self.symbol {
            return Err(QuantGridError::Trading(
                format!("订单交易对 {} 与订单簿交易对 {} 不匹配", order.symbol, self.symbol)
            ));
        }
        
        if order.quantity <= Decimal::ZERO {
            return Err(QuantGridError::Trading("订单数量必须大于0".to_string()));
        }
        
        if order.price <= Decimal::ZERO {
            return Err(QuantGridError::Trading("订单价格必须大于0".to_string()));
        }
        
        // 设置订单状态为等待
        order.status = OrderStatus::Pending;
        order.updated_at = chrono::Utc::now();
        
        // 生成价格key (保留足够精度并确保排序正确)
        let price_key = self.generate_price_key(order.price, order.order_type);
        
        // 添加到相应的订单簿
        match order.order_type {
            OrderType::Buy => {
                self.buy_orders.insert(price_key, order.clone());
            }
            OrderType::Sell => {
                self.sell_orders.insert(price_key, order.clone());
            }
        }
        
        // 添加到订单映射
        self.order_map.insert(order.id.clone(), order.clone());
        
        debug!("添加订单到订单簿: {} 类型: {:?} 价格: {} 数量: {}", 
               order.id, order.order_type, order.price, order.quantity);
        
        Ok(())
    }
    
    /// 生成价格key，确保正确的排序
    fn generate_price_key(&self, price: Decimal, order_type: OrderType) -> String {
        match order_type {
            // 买入订单：价格从高到低排序，所以使用负数
            OrderType::Buy => format!("{:020}", (Decimal::MAX - price).to_string().replace('.', "")),
            // 卖出订单：价格从低到高排序
            OrderType::Sell => format!("{:020}", price.to_string().replace('.', "")),
        }
    }
    
    /// 移除订单
    pub fn remove_order(&mut self, order_id: &str) -> Result<Option<VirtualOrder>> {
        if let Some(order) = self.order_map.remove(order_id) {
            let price_key = self.generate_price_key(order.price, order.order_type);
            
            match order.order_type {
                OrderType::Buy => {
                    self.buy_orders.remove(&price_key);
                }
                OrderType::Sell => {
                    self.sell_orders.remove(&price_key);
                }
            }
            
            debug!("从订单簿移除订单: {}", order_id);
            Ok(Some(order))
        } else {
            warn!("尝试移除不存在的订单: {}", order_id);
            Ok(None)
        }
    }
    
    /// 更新订单状态
    pub fn update_order_status(&mut self, order_id: &str, status: OrderStatus) -> Result<()> {
        if let Some(order) = self.order_map.get_mut(order_id) {
            order.status = status;
            order.updated_at = chrono::Utc::now();
            
            // 同步更新订单簿中的订单
            let price_key = self.generate_price_key(order.price, order.order_type);
            match order.order_type {
                OrderType::Buy => {
                    if let Some(book_order) = self.buy_orders.get_mut(&price_key) {
                        book_order.status = status;
                        book_order.updated_at = order.updated_at;
                    }
                }
                OrderType::Sell => {
                    if let Some(book_order) = self.sell_orders.get_mut(&price_key) {
                        book_order.status = status;
                        book_order.updated_at = order.updated_at;
                    }
                }
            }
            
            debug!("更新订单状态: {} -> {:?}", order_id, status);
            Ok(())
        } else {
            Err(QuantGridError::Trading(format!("订单不存在: {}", order_id)))
        }
    }
    
    /// 更新市场价格并检查可触发的订单
    pub fn update_market_price(&mut self, price: Decimal) -> Result<Vec<String>> {
        self.current_price = Some(price);
        
        let mut triggered_orders = Vec::new();
        
        // 检查买入订单 (当前价格 <= 买入价格时触发)
        for (_, order) in &self.buy_orders {
            if order.status == OrderStatus::Pending && price <= order.price {
                triggered_orders.push(order.id.clone());
            }
        }
        
        // 检查卖出订单 (当前价格 >= 卖出价格时触发)
        for (_, order) in &self.sell_orders {
            if order.status == OrderStatus::Pending && price >= order.price {
                triggered_orders.push(order.id.clone());
            }
        }
        
        if !triggered_orders.is_empty() {
            debug!("价格更新到 {}，触发 {} 个订单", price, triggered_orders.len());
        }
        
        Ok(triggered_orders)
    }
    
    /// 获取订单
    pub fn get_order(&self, order_id: &str) -> Option<&VirtualOrder> {
        self.order_map.get(order_id)
    }
    
    /// 获取所有订单
    pub fn get_all_orders(&self) -> Vec<&VirtualOrder> {
        self.order_map.values().collect()
    }
    
    /// 获取待处理订单
    pub fn get_pending_orders(&self) -> Vec<&VirtualOrder> {
        self.order_map.values()
            .filter(|order| order.status == OrderStatus::Pending)
            .collect()
    }
    
    /// 获取买入订单 (按价格从高到低排序)
    pub fn get_buy_orders(&self) -> Vec<&VirtualOrder> {
        self.buy_orders.values().collect()
    }
    
    /// 获取卖出订单 (按价格从低到高排序)
    pub fn get_sell_orders(&self) -> Vec<&VirtualOrder> {
        self.sell_orders.values().collect()
    }
    
    /// 获取最佳买入价格 (最高买入价)
    pub fn get_best_bid(&self) -> Option<Decimal> {
        self.buy_orders.values()
            .filter(|order| order.status == OrderStatus::Pending)
            .map(|order| order.price)
            .max()
    }
    
    /// 获取最佳卖出价格 (最低卖出价)
    pub fn get_best_ask(&self) -> Option<Decimal> {
        self.sell_orders.values()
            .filter(|order| order.status == OrderStatus::Pending)
            .map(|order| order.price)
            .min()
    }
    
    /// 获取价差
    pub fn get_spread(&self) -> Option<Decimal> {
        if let (Some(bid), Some(ask)) = (self.get_best_bid(), self.get_best_ask()) {
            Some(ask - bid)
        } else {
            None
        }
    }
    
    /// 获取当前市场价格
    pub fn get_current_price(&self) -> Option<Decimal> {
        self.current_price
    }
    
    /// 获取交易对符号
    pub fn get_symbol(&self) -> &str {
        &self.symbol
    }
    
    /// 获取订单统计信息
    pub fn get_order_stats(&self) -> OrderStats {
        let total_orders = self.order_map.len();
        let pending_orders = self.get_pending_orders().len();
        let filled_orders = self.order_map.values()
            .filter(|order| order.status == OrderStatus::Filled)
            .count();
        let cancelled_orders = self.order_map.values()
            .filter(|order| order.status == OrderStatus::Cancelled)
            .count();
        let failed_orders = self.order_map.values()
            .filter(|order| order.status == OrderStatus::Failed)
            .count();
        
        let buy_orders = self.buy_orders.len();
        let sell_orders = self.sell_orders.len();
        
        OrderStats {
            total_orders,
            pending_orders,
            filled_orders,
            cancelled_orders,
            failed_orders,
            buy_orders,
            sell_orders,
        }
    }
    
    /// 清理已完成的订单 (可选择保留历史记录)
    pub fn cleanup_completed_orders(&mut self, keep_history: bool) -> usize {
        if keep_history {
            return 0; // 保留所有订单历史
        }
        
        let mut removed_count = 0;
        let mut orders_to_remove = Vec::new();
        
        // 收集需要移除的订单ID
        for (order_id, order) in &self.order_map {
            if matches!(order.status, OrderStatus::Filled | OrderStatus::Cancelled | OrderStatus::Failed) {
                orders_to_remove.push(order_id.clone());
            }
        }
        
        // 移除订单
        for order_id in orders_to_remove {
            if self.remove_order(&order_id).is_ok() {
                removed_count += 1;
            }
        }
        
        if removed_count > 0 {
            info!("清理了 {} 个已完成的订单", removed_count);
        }
        
        removed_count
    }
}

/// 订单统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderStats {
    pub total_orders: usize,
    pub pending_orders: usize,
    pub filled_orders: usize,
    pub cancelled_orders: usize,
    pub failed_orders: usize,
    pub buy_orders: usize,
    pub sell_orders: usize,
}
