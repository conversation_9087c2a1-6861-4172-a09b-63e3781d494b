//! Quant Grid RS - 去中心化交易所量化交易系统
//! 
//! 这是一个基于Rust的去中心化交易所量化交易系统，使用动态网格策略进行自动化交易。
//! 系统采用模块化设计，支持多链多DEX交易。

pub mod core;
pub mod data;
pub mod blockchain;
pub mod security;
pub mod api;
pub mod utils;

// 重新导出核心类型
pub use core::{
    grid_strategy::GridStrategy,
    trading_engine::TradingEngine,
    virtual_orderbook::VirtualOrderBook,
};

pub use data::{
    market_data::MarketDataProvider,
    storage::DatabaseManager,
};

pub use blockchain::{
    bsc::BscClient,
    wallet::WalletManager,
};

pub use security::{
    encryption::EncryptionManager,
    auth::AuthManager,
};

pub use utils::{
    config::Config,
    logger::init_logger,
};

/// 系统错误类型
#[derive(thiserror::Error, Debug)]
pub enum QuantGridError {
    #[error("配置错误: {0}")]
    Config(String),
    
    #[error("数据库错误: {0}")]
    Database(#[from] rusqlite::Error),
    
    #[error("区块链错误: {0}")]
    Blockchain(String),
    
    #[error("交易错误: {0}")]
    Trading(String),
    
    #[error("安全错误: {0}")]
    Security(String),
    
    #[error("网络错误: {0}")]
    Network(String),

    #[error("数据错误: {0}")]
    Data(String),

    #[error("序列化错误: {0}")]
    Serialization(#[from] serde_json::Error),
    
    #[error("其他错误: {0}")]
    Other(#[from] anyhow::Error),
}

/// 系统结果类型
pub type Result<T> = std::result::Result<T, QuantGridError>;

/// 系统常量
pub mod constants {
    /// 默认网格数量
    pub const DEFAULT_GRID_COUNT: u32 = 10;
    
    /// 默认价格偏差百分比
    pub const DEFAULT_PRICE_DEVIATION: f64 = 0.02; // 2%
    
    /// 最小交易金额 (USDT)
    pub const MIN_TRADE_AMOUNT: f64 = 10.0;
    
    /// 最大滑点容忍度
    pub const MAX_SLIPPAGE_TOLERANCE: f64 = 0.005; // 0.5%
    
    /// 数据刷新间隔 (秒)
    pub const DATA_REFRESH_INTERVAL: u64 = 5;
    
    /// 订单检查间隔 (秒)
    pub const ORDER_CHECK_INTERVAL: u64 = 3;
}
