//! 网格策略演示程序
//! 
//! 展示如何使用网格策略进行模拟交易

use quant_grid_rs::{
    core::{GridConfig, GridStrategy},
    data::MockMarketDataProvider,
    Result, info,
};
use rust_decimal::Decimal;
use rust_decimal::prelude::*;

#[tokio::main]
async fn main() -> Result<()> {
    println!("🚀 网格策略演示程序启动");
    
    // 创建网格配置
    let grid_config = GridConfig {
        symbol: "BNB/USDT".to_string(),
        grid_count: 10,
        price_lower: Decimal::from(280),
        price_upper: Decimal::from(320),
        trade_amount: Decimal::from(100),
        dynamic_adjustment: true,
        volatility_period: 60,
        adjustment_threshold: Decimal::from_f64(0.02).unwrap(),
    };
    
    println!("📊 网格配置:");
    println!("  交易对: {}", grid_config.symbol);
    println!("  网格数量: {}", grid_config.grid_count);
    println!("  价格区间: {} - {}", grid_config.price_lower, grid_config.price_upper);
    println!("  单笔交易金额: {} USDT", grid_config.trade_amount);
    
    // 创建网格策略
    let mut grid_strategy = GridStrategy::new(grid_config)?;
    
    // 创建模拟市场数据提供者
    let mut market_data = MockMarketDataProvider::new();
    market_data.set_price("BNB/USDT", Decimal::from(300));
    
    // 获取当前价格并初始化网格
    let current_price = market_data.get_price("BNB/USDT").await?.price;
    println!("💰 当前价格: {} USDT", current_price);
    
    grid_strategy.initialize(current_price)?;
    
    // 显示初始网格订单
    let orders = grid_strategy.get_grid_orders();
    println!("\n📋 初始网格订单 ({} 个):", orders.len());
    for order in orders {
        println!("  {} - {:?} @ {} USDT (数量: {})", 
                 order.id, order.order_type, order.price, order.quantity);
    }
    
    // 模拟价格变化
    println!("\n📈 模拟价格变化:");
    let price_changes = vec![
        Decimal::from(295), // 价格下跌，可能触发买入订单
        Decimal::from(290),
        Decimal::from(305), // 价格上涨，可能触发卖出订单
        Decimal::from(310),
        Decimal::from(285), // 继续下跌
        Decimal::from(315), // 大幅上涨
    ];
    
    for new_price in price_changes {
        println!("\n🔄 价格更新: {} USDT", new_price);
        
        // 更新价格并检查触发的订单
        let triggered_orders = grid_strategy.update_price(new_price)?;
        
        if !triggered_orders.is_empty() {
            println!("  ⚡ 触发订单:");
            for order_id in &triggered_orders {
                if let Some(order) = grid_strategy.get_grid_orders().iter().find(|o| &o.id == order_id) {
                    println!("    {} - {:?} @ {} USDT", 
                             order.id, order.order_type, order.price);
                }
                
                // 模拟订单成交
                grid_strategy.mark_order_filled(order_id)?;
                println!("    ✅ 订单已成交: {}", order_id);
            }
        } else {
            println!("  📊 无订单触发");
        }
        
        // 显示当前网格状态
        let current_orders = grid_strategy.get_grid_orders();
        let pending_orders = current_orders.iter()
            .filter(|o| o.status == quant_grid_rs::core::OrderStatus::Pending)
            .count();
        let filled_orders = current_orders.iter()
            .filter(|o| o.status == quant_grid_rs::core::OrderStatus::Filled)
            .count();
        
        println!("  📊 网格状态: {} 待处理, {} 已成交", pending_orders, filled_orders);
    }
    
    // 最终统计
    println!("\n📊 最终统计:");
    let final_orders = grid_strategy.get_grid_orders();
    let total_orders = final_orders.len();
    let filled_orders = final_orders.iter()
        .filter(|o| o.status == quant_grid_rs::core::OrderStatus::Filled)
        .count();
    let pending_orders = final_orders.iter()
        .filter(|o| o.status == quant_grid_rs::core::OrderStatus::Pending)
        .count();
    
    println!("  总订单数: {}", total_orders);
    println!("  已成交订单: {}", filled_orders);
    println!("  待处理订单: {}", pending_orders);
    println!("  网格间距: {}", grid_strategy.get_grid_spacing());
    
    println!("\n✅ 演示完成!");
    
    Ok(())
}
