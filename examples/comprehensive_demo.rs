//! 综合演示示例
//! 
//! 展示量化网格交易系统的完整功能，包括：
//! - 真实区块链连接
//! - 真实市场数据获取
//! - 网格策略配置
//! - 虚拟订单簿管理
//! - 风险管理

use quant_grid_rs::{
    blockchain::bsc::BscClient,
    data::{
        market_data::MarketDataProvider,
        binance_provider::BinanceMarketDataProvider,
    },
    core::{
        grid_strategy::{GridStrategy, GridConfig},
        virtual_orderbook::VirtualOrderBook,
        risk_manager::RiskManager,
    },
};
use rust_decimal::Decimal;
use std::str::FromStr;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 量化网格交易系统 - 综合演示");
    println!("═══════════════════════════════════════════════════════════");
    
    // 1. 区块链连接测试
    println!("\n📡 1. 区块链连接测试");
    println!("─────────────────────────────");
    
    let mut bsc_client = BscClient::new(
        "https://bsc-dataseed1.binance.org/".to_string(),
        "wss://bsc-ws-node.nariox.org:443".to_string(),
        56, // BSC 主网
        5,  // Gas价格
        300000, // Gas限制
    );
    
    match bsc_client.connect().await {
        Ok(_) => {
            println!("✅ BSC 网络连接成功");
            println!("   链ID: {}", bsc_client.get_chain_id());
            
            // 查询一个测试地址的余额
            let test_address = "0x8894E0a0c962CB723c1976a4421c95949bE2D4E3";
            match bsc_client.get_balance(test_address).await {
                Ok(balance) => {
                    println!("   测试地址余额: {} BNB", balance);
                }
                Err(e) => {
                    println!("   ⚠️  余额查询失败: {}", e);
                }
            }
        }
        Err(e) => {
            println!("❌ BSC 网络连接失败: {}", e);
        }
    }
    
    // 2. 市场数据获取测试
    println!("\n📊 2. 市场数据获取测试");
    println!("─────────────────────────────");
    
    let market_provider = BinanceMarketDataProvider::new();
    let symbol = "BNB/USDT";
    
    match market_provider.get_price(symbol).await {
        Ok(price_data) => {
            println!("✅ {} 当前价格: ${}", symbol, price_data.price);
            println!("   数据源: {}", price_data.source);
            println!("   时间戳: {}", price_data.timestamp.format("%Y-%m-%d %H:%M:%S UTC"));
            
            // 3. 网格策略配置
            println!("\n⚙️  3. 网格策略配置");
            println!("─────────────────────────────");
            
            let current_price = price_data.price;
            let grid_config = GridConfig {
                symbol: symbol.to_string(),
                grid_count: 10,
                price_lower: current_price * Decimal::from_str("0.80").unwrap(), // -20%
                price_upper: current_price * Decimal::from_str("1.20").unwrap(), // +20%
                trade_amount: Decimal::from_str("100").unwrap(),  // 100 USDT per order
                dynamic_adjustment: true,
                volatility_period: 60, // 60分钟
                adjustment_threshold: Decimal::from_str("0.05").unwrap(), // 5%
            };

            println!("✅ 网格策略配置完成:");
            println!("   交易对: {}", grid_config.symbol);
            println!("   当前价格: ${}", current_price);
            println!("   网格数量: {}", grid_config.grid_count);
            println!("   单笔金额: ${}", grid_config.trade_amount);
            println!("   价格区间: ${} - ${}", grid_config.price_lower, grid_config.price_upper);
            println!("   动态调整: {}", grid_config.dynamic_adjustment);
            
            // 4. 创建网格策略
            println!("\n🎯 4. 网格策略初始化");
            println!("─────────────────────────────");

            match GridStrategy::new(grid_config.clone()) {
                Ok(mut grid_strategy) => {
                    match grid_strategy.initialize(current_price) {
                        Ok(_) => {
                            println!("✅ 网格策略初始化成功");
                            let orders = grid_strategy.get_grid_orders();
                            println!("   生成网格订单: {} 个", orders.len());
                            println!("   网格间距: ${}", grid_strategy.get_grid_spacing());

                            // 显示前5个网格订单
                            println!("   前5个网格订单:");
                            for (i, order) in orders.iter().take(5).enumerate() {
                                println!("     {}. 价格: ${}, 类型: {:?}, 数量: {}",
                                         i + 1, order.price, order.order_type, order.quantity);
                            }
                        }
                        Err(e) => {
                            println!("❌ 网格策略初始化失败: {}", e);
                        }
                    }
                }
                Err(e) => {
                    println!("❌ 网格策略创建失败: {}", e);
                }
            }
            
            // 5. 虚拟订单簿管理
            println!("\n📋 5. 虚拟订单簿管理");
            println!("─────────────────────────────");

            let mut orderbook = VirtualOrderBook::new(symbol.to_string());

            // 模拟添加一些订单
            let buy_price = current_price * Decimal::from_str("0.98").unwrap();
            let sell_price = current_price * Decimal::from_str("1.02").unwrap();

            // 创建虚拟订单
            use quant_grid_rs::core::virtual_orderbook::{VirtualOrder, OrderType as VOrderType, OrderStatus as VOrderStatus};

            let buy_order = VirtualOrder {
                id: "test_buy_1".to_string(),
                symbol: symbol.to_string(),
                order_type: VOrderType::Buy,
                quantity: Decimal::from_str("1.0").unwrap(),
                price: buy_price,
                status: VOrderStatus::Pending,
                created_at: chrono::Utc::now(),
                updated_at: chrono::Utc::now(),
                grid_level: Some(1),
                slippage_tolerance: Decimal::from_str("0.01").unwrap(), // 1%
            };

            let sell_order = VirtualOrder {
                id: "test_sell_1".to_string(),
                symbol: symbol.to_string(),
                order_type: VOrderType::Sell,
                quantity: Decimal::from_str("1.0").unwrap(),
                price: sell_price,
                status: VOrderStatus::Pending,
                created_at: chrono::Utc::now(),
                updated_at: chrono::Utc::now(),
                grid_level: Some(2),
                slippage_tolerance: Decimal::from_str("0.01").unwrap(), // 1%
            };

            match orderbook.add_order(buy_order) {
                Ok(_) => println!("✅ 买单添加成功: {} BNB @ ${}", 1.0, buy_price),
                Err(e) => println!("❌ 买单添加失败: {}", e),
            }

            match orderbook.add_order(sell_order) {
                Ok(_) => println!("✅ 卖单添加成功: {} BNB @ ${}", 1.0, sell_price),
                Err(e) => println!("❌ 卖单添加失败: {}", e),
            }

            let stats = orderbook.get_order_stats();
            println!("   订单簿统计:");
            println!("     总订单数: {}", stats.total_orders);
            println!("     买单数: {}", stats.buy_orders);
            println!("     卖单数: {}", stats.sell_orders);
            println!("     待处理订单: {}", stats.pending_orders);
            
            // 6. 风险管理
            println!("\n🛡️  6. 风险管理");
            println!("─────────────────────────────");

            use quant_grid_rs::core::risk_manager::RiskConfig;

            let risk_config = RiskConfig {
                max_trade_amount: Decimal::from_str("1000").unwrap(),    // 1000 USDT 最大单笔
                max_daily_amount: Decimal::from_str("10000").unwrap(),   // 10000 USDT 最大日交易
                max_position_ratio: Decimal::from_str("0.8").unwrap(),   // 80% 最大仓位比例
                max_slippage: Decimal::from_str("0.02").unwrap(),        // 2% 最大滑点
                max_consecutive_losses: 5,                               // 最大连续亏损次数
                stop_loss_ratio: Decimal::from_str("0.05").unwrap(),     // 5% 止损比例
                enabled: true,                                           // 启用风险控制
            };

            let risk_manager = RiskManager::new(risk_config);

            // 模拟检查风险
            let portfolio_value = Decimal::from_str("10000").unwrap(); // $10,000
            let position_size = Decimal::from_str("1000").unwrap();    // $1,000

            // 计算仓位比例
            let position_ratio = position_size / portfolio_value;
            println!("✅ 仓位检查: ${} / ${} = {:.2}%", position_size, portfolio_value, position_ratio * Decimal::from(100));

            // 测试风险检查
            let risk_result = risk_manager.check_trade_risk(
                Decimal::from_str("500").unwrap(),  // 交易金额
                current_price,                      // 当前价格
                current_price * Decimal::from_str("1.01").unwrap(), // 目标价格 (+1%)
            );

            if risk_result.passed {
                println!("✅ 风险检查通过");
            } else {
                println!("❌ 风险检查失败: {:?}", risk_result.errors);
            }

            if !risk_result.warnings.is_empty() {
                println!("⚠️  风险警告: {:?}", risk_result.warnings);
            }

            println!("   风险管理配置:");
            println!("     最大单笔交易: ${}", risk_manager.get_config().max_trade_amount);
            println!("     最大日交易: ${}", risk_manager.get_config().max_daily_amount);
            println!("     最大仓位比例: {:.1}%", risk_manager.get_config().max_position_ratio * Decimal::from(100));
            println!("     最大滑点: {:.1}%", risk_manager.get_config().max_slippage * Decimal::from(100));
            
            // 7. 获取K线数据进行技术分析
            println!("\n📈 7. 技术分析数据");
            println!("─────────────────────────────");
            
            match market_provider.get_klines(symbol, "1h", Some(5)).await {
                Ok(klines) => {
                    println!("✅ 获取到 {} 条K线数据", klines.len());
                    
                    if !klines.is_empty() {
                        let latest = &klines[klines.len() - 1];
                        println!("   最新K线数据:");
                        println!("     开盘: ${}", latest.open);
                        println!("     最高: ${}", latest.high);
                        println!("     最低: ${}", latest.low);
                        println!("     收盘: ${}", latest.close);
                        println!("     成交量: {}", latest.volume);
                        
                        // 简单的技术指标计算
                        let price_range = latest.high - latest.low;
                        let price_change_pct = ((latest.close - latest.open) / latest.open) * Decimal::from(100);
                        
                        println!("   技术指标:");
                        println!("     价格区间: ${}", price_range);
                        println!("     涨跌幅: {:+.2}%", price_change_pct);
                    }
                }
                Err(e) => {
                    println!("❌ K线数据获取失败: {}", e);
                }
            }
        }
        Err(e) => {
            println!("❌ 价格数据获取失败: {}", e);
        }
    }
    
    // 8. 系统状态总结
    println!("\n📊 8. 系统状态总结");
    println!("─────────────────────────────");
    println!("✅ 区块链集成: BSC 网络连接正常");
    println!("✅ 市场数据: Binance API 集成正常");
    println!("✅ 网格策略: 配置和初始化完成");
    println!("✅ 订单管理: 虚拟订单簿运行正常");
    println!("✅ 风险控制: 风险管理模块正常");
    println!("✅ 技术分析: K线数据获取正常");
    
    println!("\n🎉 综合演示完成！");
    println!("💡 系统已准备好进行实际交易（需要配置真实钱包）");
    
    Ok(())
}
