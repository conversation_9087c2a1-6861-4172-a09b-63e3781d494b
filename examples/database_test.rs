//! 数据库测试示例
//! 
//! 测试真实的 SQLite 数据库功能

use quant_grid_rs::data::storage::{DatabaseManager, BotConfig, TradeRecord};
use rust_decimal::Decimal;
use std::str::FromStr;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 开始数据库测试...");
    
    // 创建数据库管理器
    let mut db_manager = DatabaseManager::new("test_quant_grid.db".to_string());
    
    println!("📡 连接到数据库...");
    
    // 连接数据库
    match db_manager.connect().await {
        Ok(_) => {
            println!("✅ 数据库连接成功！");
            
            // 测试保存机器人配置
            println!("\n💾 测试机器人配置保存...");
            
            let bot_config = BotConfig {
                id: "test_bot_1".to_string(),
                name: "测试网格机器人".to_string(),
                symbol: "BNB/USDT".to_string(),
                config_json: r#"{"grid_count":10,"price_lower":"600","price_upper":"700","trade_amount":"100"}"#.to_string(),
                created_at: chrono::Utc::now(),
                updated_at: chrono::Utc::now(),
                enabled: true,
            };
            
            match db_manager.save_bot_config(&bot_config).await {
                Ok(_) => {
                    println!("✅ 机器人配置保存成功: {}", bot_config.id);
                    
                    // 测试加载机器人配置
                    println!("\n📖 测试机器人配置加载...");
                    
                    match db_manager.load_bot_config(&bot_config.id).await {
                        Ok(Some(loaded_config)) => {
                            println!("✅ 机器人配置加载成功:");
                            println!("   ID: {}", loaded_config.id);
                            println!("   名称: {}", loaded_config.name);
                            println!("   交易对: {}", loaded_config.symbol);
                            println!("   启用状态: {}", loaded_config.enabled);
                            println!("   配置JSON: {}", loaded_config.config_json);
                        }
                        Ok(None) => {
                            println!("❌ 机器人配置不存在");
                        }
                        Err(e) => {
                            println!("❌ 机器人配置加载失败: {}", e);
                        }
                    }
                    
                    // 测试保存交易记录
                    println!("\n💰 测试交易记录保存...");
                    
                    let trade_record = TradeRecord {
                        id: "trade_001".to_string(),
                        bot_id: bot_config.id.clone(),
                        symbol: "BNB/USDT".to_string(),
                        side: "buy".to_string(),
                        quantity: Decimal::from_str("1.5").unwrap(),
                        price: Decimal::from_str("650.25").unwrap(),
                        total_amount: Decimal::from_str("975.375").unwrap(),
                        fee: Decimal::from_str("0.975").unwrap(),
                        tx_hash: Some("0x1234567890abcdef".to_string()),
                        status: "completed".to_string(),
                        created_at: chrono::Utc::now(),
                        executed_at: Some(chrono::Utc::now()),
                    };
                    
                    match db_manager.save_trade_record(&trade_record).await {
                        Ok(_) => {
                            println!("✅ 交易记录保存成功: {}", trade_record.id);
                            
                            // 测试查询交易记录
                            println!("\n📊 测试交易记录查询...");
                            
                            match db_manager.get_trade_records(&bot_config.id, Some(10)).await {
                                Ok(records) => {
                                    println!("✅ 查询到 {} 条交易记录", records.len());
                                    
                                    for (i, record) in records.iter().enumerate() {
                                        println!("   {}. {} {} {} BNB @ ${} (总额: ${})",
                                                 i + 1,
                                                 record.side.to_uppercase(),
                                                 record.symbol,
                                                 record.quantity,
                                                 record.price,
                                                 record.total_amount
                                        );
                                    }
                                }
                                Err(e) => {
                                    println!("❌ 交易记录查询失败: {}", e);
                                }
                            }
                        }
                        Err(e) => {
                            println!("❌ 交易记录保存失败: {}", e);
                        }
                    }
                    
                    // 测试获取所有机器人配置
                    println!("\n📋 测试获取所有机器人配置...");
                    
                    match db_manager.get_all_bot_configs().await {
                        Ok(configs) => {
                            println!("✅ 查询到 {} 个机器人配置", configs.len());
                            
                            for (i, config) in configs.iter().enumerate() {
                                println!("   {}. {} ({}) - {} - 启用: {}",
                                         i + 1,
                                         config.name,
                                         config.id,
                                         config.symbol,
                                         config.enabled
                                );
                            }
                        }
                        Err(e) => {
                            println!("❌ 获取机器人配置失败: {}", e);
                        }
                    }
                    
                    // 测试保存价格数据
                    println!("\n💹 测试价格数据保存...");
                    
                    match db_manager.save_price_data(
                        "BNB/USDT",
                        Decimal::from_str("650.75").unwrap(),
                        Some(Decimal::from_str("1234567.89").unwrap()),
                        "binance"
                    ).await {
                        Ok(_) => {
                            println!("✅ 价格数据保存成功");
                        }
                        Err(e) => {
                            println!("❌ 价格数据保存失败: {}", e);
                        }
                    }
                }
                Err(e) => {
                    println!("❌ 机器人配置保存失败: {}", e);
                }
            }
            
            // 断开数据库连接
            println!("\n🔌 断开数据库连接...");
            match db_manager.disconnect().await {
                Ok(_) => println!("✅ 数据库连接已断开"),
                Err(e) => println!("❌ 断开连接失败: {}", e),
            }
        }
        Err(e) => {
            println!("❌ 数据库连接失败: {}", e);
        }
    }
    
    println!("\n🎉 数据库测试完成！");
    println!("💡 提示: 数据已保存到 test_quant_grid.db 文件中");
    
    Ok(())
}
