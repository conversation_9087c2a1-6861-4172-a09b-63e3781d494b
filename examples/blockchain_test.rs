//! 区块链连接测试示例
//! 
//! 测试真实的 BSC 网络连接和基本功能

use quant_grid_rs::blockchain::bsc::BscClient;
use rust_decimal::Decimal;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 开始区块链连接测试...");
    
    // 创建 BSC 客户端
    let mut bsc_client = BscClient::new(
        "https://bsc-dataseed1.binance.org/".to_string(),
        "wss://bsc-ws-node.nariox.org:443".to_string(),
        56, // BSC 主网链ID
        5,  // Gas价格 (Gwei)
        300000, // Gas限制
    );
    
    println!("📡 连接到 BSC 网络...");
    
    // 连接到网络
    match bsc_client.connect().await {
        Ok(_) => {
            println!("✅ 成功连接到 BSC 网络！");
            
            // 测试查询一个知名地址的余额
            let test_address = "******************************************"; // Binance Hot Wallet
            
            println!("🔍 查询地址余额: {}", test_address);
            
            match bsc_client.get_balance(test_address).await {
                Ok(balance) => {
                    println!("💰 地址余额: {} BNB", balance);
                    
                    if balance > Decimal::ZERO {
                        println!("✅ 余额查询测试通过！");
                    } else {
                        println!("⚠️  余额为零，可能是测试地址问题");
                    }
                }
                Err(e) => {
                    println!("❌ 余额查询失败: {}", e);
                }
            }
            
            // 测试连接状态
            if bsc_client.is_connected() {
                println!("✅ 客户端连接状态正常");
                println!("🔗 链ID: {}", bsc_client.get_chain_id());
            }
            
            // 断开连接
            println!("🔌 断开网络连接...");
            bsc_client.disconnect().await?;
            println!("✅ 已断开连接");
        }
        Err(e) => {
            println!("❌ 连接失败: {}", e);
            println!("💡 提示: 请检查网络连接或 RPC 端点是否可用");
        }
    }
    
    println!("🎉 区块链连接测试完成！");
    Ok(())
}
