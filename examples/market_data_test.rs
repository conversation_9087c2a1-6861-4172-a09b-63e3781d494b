//! 市场数据测试示例
//! 
//! 测试真实的 Binance API 市场数据获取

use quant_grid_rs::data::{
    market_data::MarketDataProvider,
    binance_provider::BinanceMarketDataProvider,
};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 开始市场数据测试...");
    
    // 创建 Binance 数据提供者
    let provider = BinanceMarketDataProvider::new();
    
    println!("📊 测试价格数据获取...");
    
    // 测试多个交易对的价格
    let symbols = vec!["BTC/USDT", "BNB/USDT", "ETH/USDT"];
    
    for symbol in &symbols {
        println!("\n🔍 获取 {} 价格...", symbol);
        
        match provider.get_price(symbol).await {
            Ok(price_data) => {
                println!("✅ {} 当前价格: ${}", symbol, price_data.price);
                println!("   时间戳: {}", price_data.timestamp.format("%Y-%m-%d %H:%M:%S UTC"));
            }
            Err(e) => {
                println!("❌ {} 价格获取失败: {}", symbol, e);
            }
        }
    }
    
    println!("\n📈 测试K线数据获取...");
    
    // 测试获取 BNB/USDT 的1小时K线数据
    let symbol = "BNB/USDT";
    println!("\n🔍 获取 {} 1小时K线数据 (最近10条)...", symbol);
    
    match provider.get_klines(symbol, "1h", Some(10)).await {
        Ok(klines) => {
            println!("✅ 成功获取 {} 条K线数据", klines.len());
            
            if !klines.is_empty() {
                println!("\n📊 最近的K线数据:");
                println!("时间                    开盘价    最高价    最低价    收盘价    成交量");
                println!("─────────────────────────────────────────────────────────────────");
                
                for (i, kline) in klines.iter().rev().take(5).enumerate() {
                    let time_str = kline.timestamp.format("%m-%d %H:%M");
                    println!("{:2}. {} {:>8} {:>8} {:>8} {:>8} {:>10}",
                             i + 1,
                             time_str,
                             format!("{:.2}", kline.open),
                             format!("{:.2}", kline.high),
                             format!("{:.2}", kline.low),
                             format!("{:.2}", kline.close),
                             format!("{:.0}", kline.volume)
                    );
                }
                
                // 计算一些基本统计
                let latest = &klines[klines.len() - 1];
                let oldest = &klines[0];
                let price_change = latest.close - oldest.open;
                let price_change_pct = (price_change / oldest.open) * rust_decimal::Decimal::from(100);
                
                println!("\n📈 统计信息:");
                println!("   期间变化: {:.4} ({:+.2}%)", price_change, price_change_pct);
                println!("   最高价: {:.4}", klines.iter().map(|k| k.high).max().unwrap());
                println!("   最低价: {:.4}", klines.iter().map(|k| k.low).min().unwrap());
                
                let total_volume: rust_decimal::Decimal = klines.iter().map(|k| k.volume).sum();
                println!("   总成交量: {:.0}", total_volume);
            }
        }
        Err(e) => {
            println!("❌ K线数据获取失败: {}", e);
        }
    }
    
    println!("\n🔔 测试订阅功能...");
    
    // 测试价格订阅 (目前是占位符实现)
    match provider.subscribe_price("BTC/USDT").await {
        Ok(_) => println!("✅ 成功订阅 BTC/USDT 价格更新"),
        Err(e) => println!("❌ 订阅失败: {}", e),
    }
    
    match provider.unsubscribe_price("BTC/USDT").await {
        Ok(_) => println!("✅ 成功取消订阅 BTC/USDT 价格更新"),
        Err(e) => println!("❌ 取消订阅失败: {}", e),
    }
    
    println!("\n🎉 市场数据测试完成！");
    println!("💡 提示: 这些都是来自 Binance API 的真实市场数据");
    
    Ok(())
}
