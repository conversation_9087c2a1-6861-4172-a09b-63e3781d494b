# Quant Grid RS 🚀

一个用 Rust 编写的高性能量化网格交易系统，专为 BSC 链上的 DeFi 交易而设计。

## ✨ 特性

### 🎯 核心功能
- **智能网格策略**: 动态调整网格间距，适应市场波动
- **多链支持**: 支持 BSC 链，可扩展到其他 EVM 兼容链
- **DEX 集成**: 集成 PancakeSwap，支持自动化交易
- **风险管理**: 内置风险控制系统，保护资金安全
- **实时监控**: 实时价格监控和订单管理

### 🔧 技术特性
- **高性能**: 使用 Rust 编写，保证极致性能
- **模块化设计**: 清晰的架构，易于扩展和维护
- **异步处理**: 基于 Tokio 的异步运行时
- **安全第一**: 本地私钥加密存储
- **跨平台**: 支持 Windows、macOS、Linux

## 🏗️ 架构设计

```
src/
├── core/                   # 核心交易逻辑
│   ├── grid_strategy.rs   # 网格策略实现
│   ├── virtual_orderbook.rs # 虚拟订单簿
│   ├── trading_engine.rs  # 交易引擎
│   └── risk_manager.rs    # 风险管理
├── data/                   # 数据层
│   ├── market_data.rs     # 市场数据获取
│   ├── storage.rs         # 数据存储
│   └── cache.rs           # 数据缓存
├── blockchain/             # 区块链交互
│   ├── bsc.rs             # BSC 客户端
│   ├── wallet.rs          # 钱包管理
│   └── dex/               # DEX 集成
│       └── pancakeswap.rs # PancakeSwap 客户端
├── security/               # 安全模块
│   ├── encryption.rs      # 加密管理
│   └── auth.rs            # 认证管理
├── api/                    # API 层
│   └── handlers.rs        # Tauri 命令处理
└── utils/                  # 工具模块
    ├── config.rs          # 配置管理
    └── logger.rs          # 日志系统
```

## 🚀 快速开始

### 环境要求
- Rust 1.70+
- Node.js 18+ (用于前端开发)

### 安装依赖
```bash
# 克隆项目
git clone https://github.com/your-username/quant-grid-rs.git
cd quant-grid-rs

# 编译项目
cargo build --release
```

### 运行演示
```bash
# 运行网格策略演示
cargo run --example grid_strategy_demo
```

### 运行主程序
```bash
# 运行主程序
cargo run
```

## 📊 网格策略演示

演示程序展示了网格策略的核心功能：

1. **初始化网格**: 根据价格区间和网格数量生成买卖订单
2. **价格监控**: 实时监控市场价格变化
3. **订单触发**: 当价格触及网格价位时自动执行交易
4. **动态调整**: 根据市场波动性动态调整网格间距
5. **风险控制**: 内置多重风险控制机制

### 演示输出示例
```
🚀 网格策略演示程序启动
📊 网格配置:
  交易对: BNB/USDT
  网格数量: 10
  价格区间: 280 - 320
  单笔交易金额: 100 USDT

💰 当前价格: 300.145 USDT

📋 初始网格订单 (9 个):
  BUY @ 280 USDT (数量: 0.357)
  BUY @ 284 USDT (数量: 0.352)
  ...
  SELL @ 304 USDT (数量: 0.329)
  SELL @ 308 USDT (数量: 0.325)

📈 模拟价格变化:
🔄 价格更新: 295 USDT
  ⚡ 触发订单: BUY @ 296 USDT
  ✅ 订单已成交
```

## 🔧 配置

项目支持通过 `config.toml` 文件进行配置：

```toml
[app]
name = "Quant Grid RS"
version = "0.1.0"
environment = "development"

[blockchain.bsc]
rpc_url = "https://bsc-dataseed1.binance.org/"
chain_id = 56
gas_price = 5
gas_limit = 300000

[trading]
default_slippage = 0.005
max_slippage = 0.02
order_check_interval = 3
price_update_interval = 5
```

## 🛡️ 安全特性

- **本地私钥存储**: 私钥加密存储在本地，永不上传
- **多重签名支持**: 支持多重签名钱包
- **风险控制**: 内置止损、最大交易金额等风险控制
- **审计日志**: 完整的操作日志记录

## 🔮 未来计划

- [ ] **前端界面**: 基于 Tauri 2.0 的跨平台桌面应用
- [ ] **更多 DEX**: 集成更多去中心化交易所
- [ ] **多链支持**: 支持 Ethereum、Polygon 等更多链
- [ ] **高级策略**: 实现更多量化交易策略
- [ ] **回测系统**: 历史数据回测功能
- [ ] **云端同步**: 可选的云端配置同步

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## ⚠️ 免责声明

本软件仅供学习和研究使用。量化交易存在风险，请谨慎使用真实资金。作者不对任何交易损失承担责任。

---

**Built with ❤️ and Rust 🦀**
