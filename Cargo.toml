[package]
name = "quant-grid-rs"
version = "0.1.0"
edition = "2021"
authors = ["Your Name <<EMAIL>>"]
description = "A decentralized exchange quantitative trading system with dynamic grid strategy"
license = "MIT"

[dependencies]
# Async runtime
tokio = { version = "1.0", features = ["macros", "rt-multi-thread", "time", "signal"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
toml = "0.8"

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Time handling
chrono = { version = "0.4", features = ["serde"] }

# Decimal arithmetic for financial calculations
rust_decimal = { version = "1.32", features = ["serde-float"] }

# Random number generation
rand = "0.8"

# Web3 and blockchain
ethers = "2.0"
secp256k1 = "0.28"

# Database
rusqlite = { version = "0.29", features = ["bundled"] }

# Crypto and security
aes-gcm = "0.10"
argon2 = "0.5"
ring = "0.17"

# HTTP client
reqwest = { version = "0.11", features = ["json"] }

# Tauri (will be added when setting up UI)
# tauri = { version = "2.0", features = [] }

[dev-dependencies]
tokio-test = "0.4"
