[package]
name = "quant-grid-rs"
version = "0.1.0"
edition = "2021"
authors = ["Your Name <<EMAIL>>"]
description = "A decentralized exchange quantitative trading system with dynamic grid strategy"
license = "MIT"

[dependencies]
# Async runtime
tokio = { version = "1.0", features = ["macros", "rt-multi-thread", "time", "signal"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
toml = "0.8"

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Time handling
chrono = { version = "0.4", features = ["serde"] }

# Decimal arithmetic for financial calculations
rust_decimal = { version = "1.32", features = ["serde-float"] }

# Random number generation
rand = "0.8"

# Web3 and blockchain (will be added later)
# web3 = "0.19"
# ethers = "2.0"
# secp256k1 = "0.28"

# Database (will be added later)
# duckdb = "0.10"
# sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "sqlite"] }

# Crypto and security (will be added later)
# aes-gcm = "0.10"
# argon2 = "0.5"

# Tauri (will be added when setting up UI)
# tauri = { version = "2.0", features = [] }

[dev-dependencies]
tokio-test = "0.4"
