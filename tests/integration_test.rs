//! 集成测试
//! 
//! 测试网格策略的核心功能

use quant_grid_rs::{
    core::{GridConfig, GridStrategy, OrderStatus},
    data::{market_data::MockMarketDataProvider, MarketDataProvider},
    Result,
};
use rust_decimal::Decimal;
use rust_decimal::prelude::*;

#[tokio::test]
async fn test_grid_strategy_initialization() -> Result<()> {
    // 创建网格配置
    let config = GridConfig {
        symbol: "BNB/USDT".to_string(),
        grid_count: 5,
        price_lower: Decimal::from(280),
        price_upper: Decimal::from(320),
        trade_amount: Decimal::from(100),
        dynamic_adjustment: false,
        volatility_period: 60,
        adjustment_threshold: Decimal::from_f64(0.02).unwrap(),
    };
    
    // 创建网格策略
    let mut strategy = GridStrategy::new(config)?;
    
    // 初始化网格
    let current_price = Decimal::from(300);
    strategy.initialize(current_price)?;
    
    // 验证网格订单数量
    let orders = strategy.get_grid_orders();
    assert!(!orders.is_empty(), "应该生成网格订单");
    
    // 验证网格间距
    let spacing = strategy.get_grid_spacing();
    assert!(spacing > Decimal::ZERO, "网格间距应该大于0");
    
    println!("✅ 网格策略初始化测试通过");
    Ok(())
}

#[tokio::test]
async fn test_price_trigger() -> Result<()> {
    // 创建网格配置
    let config = GridConfig {
        symbol: "BNB/USDT".to_string(),
        grid_count: 5,
        price_lower: Decimal::from(280),
        price_upper: Decimal::from(320),
        trade_amount: Decimal::from(100),
        dynamic_adjustment: false,
        volatility_period: 60,
        adjustment_threshold: Decimal::from_f64(0.02).unwrap(),
    };
    
    let mut strategy = GridStrategy::new(config)?;
    strategy.initialize(Decimal::from(300))?;
    
    // 测试价格触发
    let triggered_orders = strategy.update_price(Decimal::from(295))?;
    
    // 应该有订单被触发
    if !triggered_orders.is_empty() {
        println!("✅ 价格触发测试通过，触发了 {} 个订单", triggered_orders.len());
    } else {
        println!("ℹ️ 当前价格未触发任何订单");
    }
    
    Ok(())
}

#[tokio::test]
async fn test_order_execution() -> Result<()> {
    let config = GridConfig {
        symbol: "BNB/USDT".to_string(),
        grid_count: 5,
        price_lower: Decimal::from(280),
        price_upper: Decimal::from(320),
        trade_amount: Decimal::from(100),
        dynamic_adjustment: false,
        volatility_period: 60,
        adjustment_threshold: Decimal::from_f64(0.02).unwrap(),
    };
    
    let mut strategy = GridStrategy::new(config)?;
    strategy.initialize(Decimal::from(300))?;
    
    // 获取第一个订单ID
    let orders = strategy.get_grid_orders();
    if let Some(first_order) = orders.first() {
        let order_id = first_order.id.clone();
        
        // 标记订单为已成交
        strategy.mark_order_filled(&order_id)?;
        
        // 验证订单状态
        let updated_orders = strategy.get_grid_orders();
        let filled_order = updated_orders.iter()
            .find(|o| o.id == order_id);
        
        if let Some(order) = filled_order {
            assert_eq!(order.status, OrderStatus::Filled, "订单应该被标记为已成交");
            println!("✅ 订单执行测试通过");
        }
    }
    
    Ok(())
}

#[tokio::test]
async fn test_market_data_provider() -> Result<()> {
    let mut provider = MockMarketDataProvider::new();
    
    // 测试获取价格
    let price_data = provider.get_price("BNB/USDT").await?;
    assert_eq!(price_data.symbol, "BNB/USDT");
    assert!(price_data.price > Decimal::ZERO);
    
    // 测试设置价格
    provider.set_price("BNB/USDT", Decimal::from(350));
    let new_price_data = provider.get_price("BNB/USDT").await?;
    // 注意：由于模拟数据提供者会添加随机波动，价格可能不完全等于350
    assert!(new_price_data.price > Decimal::from(340));
    assert!(new_price_data.price < Decimal::from(360));
    
    // 测试获取K线数据
    let klines = provider.get_klines("BNB/USDT", "1m", Some(10)).await?;
    assert_eq!(klines.len(), 10);
    
    println!("✅ 市场数据提供者测试通过");
    Ok(())
}

#[tokio::test]
async fn test_dynamic_adjustment() -> Result<()> {
    let config = GridConfig {
        symbol: "BNB/USDT".to_string(),
        grid_count: 5,
        price_lower: Decimal::from(280),
        price_upper: Decimal::from(320),
        trade_amount: Decimal::from(100),
        dynamic_adjustment: true, // 启用动态调整
        volatility_period: 60,
        adjustment_threshold: Decimal::from_f64(0.02).unwrap(),
    };
    
    let mut strategy = GridStrategy::new(config)?;
    strategy.initialize(Decimal::from(300))?;
    
    let initial_spacing = strategy.get_grid_spacing();
    
    // 模拟大幅价格变动
    strategy.update_price(Decimal::from(250))?; // 大幅下跌
    
    let new_spacing = strategy.get_grid_spacing();
    
    // 动态调整应该改变网格间距
    if new_spacing != initial_spacing {
        println!("✅ 动态调整测试通过，网格间距从 {} 调整为 {}", 
                 initial_spacing, new_spacing);
    } else {
        println!("ℹ️ 网格间距未发生变化");
    }
    
    Ok(())
}

#[tokio::test]
async fn test_comprehensive_scenario() -> Result<()> {
    println!("🧪 开始综合场景测试");
    
    // 创建网格策略
    let config = GridConfig {
        symbol: "ETH/USDT".to_string(),
        grid_count: 8,
        price_lower: Decimal::from(1800),
        price_upper: Decimal::from(2200),
        trade_amount: Decimal::from(500),
        dynamic_adjustment: true,
        volatility_period: 60,
        adjustment_threshold: Decimal::from_f64(0.03).unwrap(),
    };
    
    let mut strategy = GridStrategy::new(config)?;
    let mut provider = MockMarketDataProvider::new();
    provider.set_price("ETH/USDT", Decimal::from(2000));
    
    // 初始化
    let initial_price = provider.get_price("ETH/USDT").await?.price;
    strategy.initialize(initial_price)?;
    
    println!("📊 初始价格: {}", initial_price);
    println!("📋 初始订单数: {}", strategy.get_grid_orders().len());
    
    // 模拟一系列价格变化
    let price_sequence = vec![
        Decimal::from(1950), // 下跌
        Decimal::from(1900), // 继续下跌
        Decimal::from(2050), // 反弹
        Decimal::from(2100), // 上涨
        Decimal::from(1850), // 大幅下跌
    ];
    
    let mut total_triggered = 0;
    
    for (i, price) in price_sequence.iter().enumerate() {
        println!("\n🔄 第 {} 轮价格更新: {}", i + 1, price);
        
        let triggered = strategy.update_price(*price)?;
        total_triggered += triggered.len();
        
        if !triggered.is_empty() {
            println!("  ⚡ 触发 {} 个订单", triggered.len());
            
            // 模拟订单成交
            for order_id in triggered {
                strategy.mark_order_filled(&order_id)?;
            }
        }
        
        let current_orders = strategy.get_grid_orders();
        let pending = current_orders.iter()
            .filter(|o| o.status == OrderStatus::Pending)
            .count();
        let filled = current_orders.iter()
            .filter(|o| o.status == OrderStatus::Filled)
            .count();
        
        println!("  📊 当前状态: {} 待处理, {} 已成交", pending, filled);
    }
    
    println!("\n📈 综合测试结果:");
    println!("  总触发订单数: {}", total_triggered);
    println!("  最终网格间距: {}", strategy.get_grid_spacing());
    
    println!("✅ 综合场景测试完成");
    Ok(())
}
