[app]
name = "Quant Grid RS"
version = "0.1.0"
environment = "development"
data_dir = "./data"

[database]
path = "./data/quant_grid.db"
pool_size = 10
connection_timeout = 30

[blockchain.bsc]
rpc_url = "https://bsc-dataseed1.binance.org/"
ws_url = "wss://bsc-ws-node.nariox.org:443/"
chain_id = 56
gas_price = 5
gas_limit = 300000
confirmations = 3

[trading]
default_slippage = 0.005
max_slippage = 0.02
order_check_interval = 3
price_update_interval = 5
min_trade_amount = 10.0
max_concurrent_trades = 10

[security]
encryption_key_length = 32
password_hash_rounds = 100000
session_timeout = 60
max_login_attempts = 5

[logging]
level = "info"
file_path = "./logs/quant_grid.log"
max_file_size = 100
max_files = 10
